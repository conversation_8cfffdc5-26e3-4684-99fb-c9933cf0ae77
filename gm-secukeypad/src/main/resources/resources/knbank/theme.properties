theme.id=knbank
theme.name=Default Theme 2.0, 480px
theme.version=2.0
theme.on.color=#ffcc00
theme.on.text.color=#000000
theme.out.color=
theme.out.text.color = 
resources.version=3.0
resources.extension=png
preview.border=
# default : true
# true : enable text line
# false : disable text line
use.text.line=true
# default : false
# true : enable shift button
# false : disable shift button
use.shift=true
# Use Caps Lock
# default : false
# true : caps lock enabled
# false : caps lock disabled
use.capslock=true
# KeyPad
kp.path=kp
# half, full, none
kp.mode=full
#kp.column.count = 4, 4
kp.line.count=4
kp.button.count=4, 4, 3, 3
#inner, outer
kp.blank.mode=outer
kp.blank.count=0, 0, 1, 0
kp.margin.left=0, 0, 0, 0
kp.margin.top=0, 0, 0, 0
kp.image.file.size=550, 370
kp.image.file.margin=2, 2
kp.image.size=223, 219
kp.button.size=44, 34
kp.margin=1, 1
kp.start.xy=22, 58
#kp.move.area = 5, 5, 94, 40
kp.button.blank.size=44, 34
kp.button.delete.size=44, 34
kp.button.delete=x(0), y(3), x(0), y(3)
kp.button.clear.size=44, 34
kp.button.clear=x(1), y(3), x(1), y(3)
kp.button.refresh.size=44, 34
kp.button.refresh=x(2), y(2), x(2), y(2)
kp.button.close.size=44, 34
kp.button.close=187, 2, 210, 26
#kp.button.enter.size = 44, 34
#kp.button.enter = x(3), y(2), x(4), y(2)
kp.extend.count=1
kp.extend.1.id=button1
kp.extend.1.size=0, 293, 88, 326
kp.extend.1.button=x(2), y(3), x(3), y(3)
# action : hide, enter, clear, delete
# value : some value...
kp.extend.1.value=action:enter
#kp.extend.1.value = value:values
#kp.extend.1.value = value:values...error
# Keyboard
kb.path=kb
kb.mode=none
kb.column.count=10
kb.line.count=4
kb.button.count=10, 9, 8, 10
kb.blank.mode=inner
kb.blank.count=2, 2, 1, 2
kb.margin.left=0, 22, 44, 0
kb.margin.top=0, 0, 0, 0
kb.image.file.size=1470, 660
kb.image.file.margin=2, 2
kb.image.size=579, 292
kb.button.size=44, 34
kb.margin=1, 1
kb.start.xy=17, 96
#kb.move.area = 5, 3, 420, 37
kb.button.blank.size=44, 34
kb.button.delete.size=44, 34
kb.button.delete=x(7), y(3), x(7), y(3)
kb.button.clear.size=44, 34
kb.button.clear=x(8), y(3), x(8), y(3)
kb.button.refresh.size=44, 34
kb.button.refresh=x(9), y(3), x(9), y(3)
kb.button.close.size=44, 34
kb.button.close=535, 12, 554, 29
kb.button.enter.size=89, 34
kb.button.enter=x(7), y(2), x(8), y(2)
kb.button.space.size=179, 34
kb.button.space=x(3), y(3), x(6), y(3)
kb.button.text.size=132, 34
kb.button.text=220, y(3), 352, y(3)
kb.button.lower.size=44, 34
kb.button.lower=x(0), y(3), x(0), y(3)
kb.button.upper.size=44, 34
kb.button.upper=x(1), y(3), x(1), y(3)
kb.button.special.size=44, 34
kb.button.special=x(2), y(3), x(2), y(3)
kb.button.shift.size=89, 34
kb.button.shift=x(0), y(3), x(1), y(3)
kb.extend.count=0
#kb.extend.1.id = button1
#kb.extend.1.size = 0, 240, 69, 273
#kb.extend.1.button = x(3), y(2), x(4), y(2)
#kb.extend.1.value = 
# Number Keypad in Keyboard
#kb.number.column.count = 10
kb.number.line.count=1
kb.number.button.count=10
kb.number.blank.mode=inner
kb.number.blank.count=2
kb.number.margin.left=0
kb.number.margin.top=0
kb.number.button.blank.size=44, 34
kb.number.button.size=44, 34
kb.number.start.xy=17, 61
kb.number.margin=1, 1