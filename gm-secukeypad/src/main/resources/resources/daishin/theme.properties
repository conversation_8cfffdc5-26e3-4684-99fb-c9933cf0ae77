theme.id=daishin
theme.name=Mobile Theme 2.0
theme.version=2.0
theme.on.color=#ffcc00
theme.on.text.color=#000000
theme.out.color=
theme.out.text.color = 
resources.version=3.0
resources.extension=png
preview.border=
# default : true
# true : enable text line
# false : disable text line
use.text.line=true
# default : false
# true : enable shift button
# false : disable shift button
use.shift=true
# Use Caps Lock
# default : false
# true : caps lock enabled
# false : caps lock disabled
use.capslock=true
# KeyPad
kp.path=kp
# halt, full, none
kp.mode=full
kp.line.count=3
kp.column.count=5, 5
kp.button.count=5, 5, 4
#inner, outer
kp.blank.mode=inner
kp.blank.count=0, 0, 0
kp.margin.left=0, 0, 0
kp.margin.top=0, 0, 0
kp.image.file.size=1380, 817
kp.image.file.margin=3, 3
kp.image.size=640, 477
kp.button.size=110, 110
kp.margin=4, 4
kp.start.xy=37, 108
kp.move.area=5, 5, 570, 77
kp.button.blank.size=110, 110
kp.button.delete.size=110, 110
kp.button.delete=x(0), y(2), x(0), y(2)
kp.button.clear.size=110, 110
kp.button.clear=x(1), y(2), x(1), y(2)
kp.button.refresh.size=110, 110
kp.button.refresh=x(22), y(22), x(22), y(22)
kp.button.close.size=72, 72
kp.button.close=663, 6, 735, 78
#kp.button.enter.size = 69, 34
#kp.button.enter = x(3), y(2), x(4), y(2)
kp.extend.count=1
kp.extend.1.id=button1
kp.extend.1.size=0, 707, 338, 817
kp.extend.1.button=x(2), y(2), x(4), y(2)
# action : hide, enter, clear, delete
# value : some value...
kp.extend.1.value=action:enter
#kp.extend.1.value = value:values
#kp.extend.1.value = value:values...error
# Keyboard
kb.path=kb
kb.mode=full
kb.line.count=4
kb.column.count=10
kb.button.count=10, 9, 8, 10
kb.blank.mode=inner
kb.blank.count=2, 2, 2, 2
kb.margin.left=0, 32, 32, 0
kb.margin.top=0, 0, 0, 0
kb.image.file.size=2141, 1434
kb.image.file.margin=3, 3
kb.image.size=836, 690
kb.button.size=64, 90
kb.margin=4, 4
kb.start.xy=13, 198
kb.move.area=5, 5, 610, 77
kb.button.blank.size=64, 90
kb.button.delete.size=64, 90
kb.button.delete=x(7), y(3), x(7), y(3)
kb.button.clear.size=64, 90
kb.button.clear=x(8), y(3), x(8), y(3)
kb.button.refresh.size=64, 90
kb.button.refresh=x(9), y(3), x(9), y(3)
kb.button.close.size=70, 70
kb.button.close=750, 8, 820, 77
kb.button.enter.size=132, 90
kb.button.enter=x(7), y(2), x(8), y(2)
kb.button.space.size=268, 90
kb.button.space=x(3), y(3), x(6), y(3)
kb.button.text.size=192, 90
kb.button.text=340, y(4), 472, y(4)
kb.button.lower.size=64, 90
kb.button.lower=x(0), y(3), x(0), y(3)
kb.button.upper.size=64, 90
kb.button.upper=x(1), y(3), x(1), y(3)
kb.button.special.size=64, 90
kb.button.special=x(2), y(3), x(2), y(3)
kb.button.shift.size=132, 90
kb.button.shift=x(0), y(3), x(1), y(3)
kb.extend.count=0
#kb.extend.1.id = button1
#kb.extend.1.size = 0, 240, 69, 273
#kb.extend.1.button = x(3), y(2), x(4), y(2)
#kb.extend.1.value = 
# Number Keypad in Keyboard
kb.number.line.count=1
kb.number.column.count=10
kb.number.button.count=10
kb.number.blank.mode=inner
kb.number.blank.count=2
kb.number.margin.left=0
kb.number.margin.top=0
kb.number.button.blank.size=64, 90
kb.number.button.size=64, 90
kb.number.start.xy=13, 104
kb.number.margin=4, 4
