theme.id=mobile
theme.name=Mobile Theme 2.0
theme.version=2.0
theme.on.color=#ffcc00
theme.on.text.color=#000000
theme.out.color=
theme.out.text.color = 
resources.version=3.0
resources.extension=png
preview.border=
# default : true
# true : enable text line
# false : disable text line
use.text.line=true
# default : false
# true : enable shift button
# false : disable shift button
use.shift=true
# Use Caps Lock
# default : false
# true : caps lock enabled
# false : caps lock disabled
use.capslock=true
# Use button text from properties
# default : false
# true : button text from properties enabled
# false : button text from properties disabled
use.button.text=false
# Use tab index for WAI-ARIA(Web Accessibility initiative-Accessible Rich Internet Applications)
# default : false
# true : enable tab-index function for Web accessibility 
# false : disable tab-index function for Web accessibility
use.tabindex=true
# KeyPad
kp.path=kp
# halt, full, none
kp.mode=full
kp.line.count=3
kp.column.count=5, 5
kp.button.count=5, 5, 4
#inner, outer
kp.blank.mode=inner
kp.blank.count=1, 1, 1
kp.margin.left=0, 0, 0
kp.margin.top=0, 0, 0
kp.image.file.size=1240, 817
kp.image.file.margin=3, 3
kp.image.size=752, 478
kp.button.size=110, 110
kp.margin=4, 4
kp.start.xy=37, 108
kp.move.area=5, 5, 570, 77
kp.button.blank.size=110, 110
kp.button.delete.size=110, 110
kp.button.delete=x(0), y(2), x(0), y(2)
kp.button.clear.size=110, 110
kp.button.clear=x(1), y(2), x(1), y(2)
kp.button.refresh.size=110, 110
kp.button.refresh=x(2), y(2), x(2), y(2)
kp.button.close.size=72, 72
kp.button.close=663, 6, 735, 78
#kp.button.enter.size = 69, 34
#kp.button.enter = x(3), y(2), x(4), y(2)
kp.extend.count=1
kp.extend.1.id=button1
kp.extend.1.size=0, 707, 224, 817
kp.extend.1.button=x(3), y(2), x(4), y(2)
# action : hide, enter, clear, delete
# value : some value...
kp.extend.1.value=action:enter
#kp.extend.1.value = value:values
#kp.extend.1.value = value:values...error
#keypad tab index sequence
kp.tabidx={"1" : "close", "2" : "num1", "3" : "num2", "4" : "num3", "5" : "num4", "6" : "num5", "7" : "num6", "8" : "num7", "9" : "num8", "10" : "num9", "11" : "num10", "12" : "delete", "13" : "clear", "14" : "refresh", "15" : "ext1"}
# Keyboard
kb.path=kb
kb.mode=full
kb.line.count=4
kb.column.count=10
kb.button.count=10, 9, 8, 10
kb.blank.mode=inner
kb.blank.count=2, 2, 2, 2
kb.margin.left=0, 32, 32, 0
kb.margin.top=0, 0, 0, 0
kb.image.file.size=2141, 1434
kb.image.file.margin=3, 3
kb.image.size=836, 690
kb.button.size=64, 90
kb.margin=4, 4
kb.start.xy=13, 198
kb.move.area=5, 5, 610, 77
kb.button.blank.size=64, 90
kb.button.delete.size=64, 90
kb.button.delete=x(7), y(3), x(7), y(3)
kb.button.clear.size=64, 90
kb.button.clear=x(8), y(3), x(8), y(3)
kb.button.refresh.size=64, 90
kb.button.refresh=x(9), y(3), x(9), y(3)
kb.button.close.size=70, 70
kb.button.close=750, 8, 820, 77
kb.button.enter.size=132, 90
kb.button.enter=x(7), y(2), x(8), y(2)
kb.button.space.size=268, 90
kb.button.space=x(3), y(3), x(6), y(3)
kb.button.text.size=192, 90
kb.button.text=340, y(4), 472, y(4)
kb.button.lower.size=64, 90
kb.button.lower=x(0), y(3), x(0), y(3)
kb.button.upper.size=64, 90
kb.button.upper=x(1), y(3), x(1), y(3)
kb.button.special.size=64, 90
kb.button.special=x(2), y(3), x(2), y(3)
kb.button.shift.size=132, 90
kb.button.shift=x(0), y(3), x(1), y(3)
kb.extend.count=0
#kb.extend.1.id = button1
#kb.extend.1.size = 0, 240, 69, 273
#kb.extend.1.button = x(3), y(2), x(4), y(2)
#kb.extend.1.value = 
#Keyboard tab index sequence
kb.tabidx={"1" : "num1", "2" : "num2", "3" : "num3", "4" : "num4", "5" : "num5", "6" : "num6", "7" : "num7", "8" : "num8", "9" : "num9", "10" : "num10", "11" : "q", "12" : "w", "13" : "e", "14" : "r", "15" :  "t", "16" :  "y", "17" :  "u", "18" :  "i", "19" :  "o", "20" :  "p", "21" :  "a", "22" :  "s", "23" :  "d", "24" :  "f", "25" :  "g", "26" :  "h", "27" :  "j", "28" :  "k", "29" :  "l", "30" :  "z", "31" :  "x", "32" :  "c", "33" :  "v", "34" :  "b", "35" :  "n", "36" :  "m", "37" : "enter", "38" : "shift", "39" : "special","40" : "space", "41" : "delete", "42" : "clear", "43" : "refresh","44" : "close"}
# Number Keypad in Keyboard
kb.number.line.count=1
kb.number.column.count=10
kb.number.button.count=10
kb.number.blank.mode=inner
kb.number.blank.count=2
kb.number.margin.left=0
kb.number.margin.top=0
kb.number.button.blank.size=64, 90
kb.number.button.size=64, 90
kb.number.start.xy=13, 104
kb.number.margin=4, 4
# button text
# CASE 1 : UNICODE
#   EX) ko.button.text.delete = \uD55C\uAC1C\uC9C0\uC6C0
# CASE 2 : CONVERT UTF-8 
#   When writing texteditor
#   EX) ko.button.text.delete = XXXXXXXX_UTF8
ko.button.text.delete=\uD55C\uAC1C\uC9C0\uC6C0
ko.button.text.clear=\uBAA8\uB450\uC9C0\uC6C0
ko.button.text.close=\uB2EB\uAE30
ko.button.text.hide=\uC228\uAE30\uAE30
ko.button.text.confirm=\uD655\uC778
ko.button.text.refresh=\uBC84\uD2BC\uC11E\uAE30
ko.button.text.button=\uBC84\uD2BC
ko.button.text.lower=\uC18C\uBB38\uC790
ko.button.text.upper=\uB300\uBB38\uC790
ko.button.text.special=\uD2B9\uC218\uBB38\uC790
ko.button.text.shift=\uC26C\uD504\uD2B8
ko.button.text.space=\uACF5\uBC31
ko.button.text.link=\uB9C1\uD06C
en.button.text.delete=Delete
en.button.text.clear=Clear
en.button.text.close=Close
en.button.text.hide=Hide
en.button.text.confirm=Confirm
en.button.text.refresh=Refresh
en.button.text.button=Button
en.button.text.lower=Lower Character
en.button.text.upper=Upper Character
en.button.text.special=Special Character
en.button.text.shift=Shift
en.button.text.space=Space
en.button.text.link=Link
jp.button.text.delete=\u524A\u9664
jp.button.text.clear=\u30AF\u30EA\u30A2
jp.button.text.close=\u9589\u3058\u308B
jp.button.text.hide=\u96A0\u3059
jp.button.text.confirm=\u78BA\u8A8D
jp.button.text.refresh=\u30DC\u30BF\u30F3\u6DF7\u5408
jp.button.text.button=\u30DC\u30BF\u30F3
jp.button.text.lower=\u4E0B\u306E\u6587\u5B57
jp.button.text.upper=\u4E0A\u306E\u6587\u5B57
jp.button.text.special=\u7279\u6B8A\u6587\u5B57
jp.button.text.shift=\u30B7\u30D5\u30C8
jp.button.text.space=\u7A7A\u767D
jp.button.text.link=\u30EA\u30F3\u30AF
ch.button.text.delete=\u5220\u9664
ch.button.text.clear=\u660E\u786E
ch.button.text.close=\u5173
ch.button.text.hide=\u9690\u85CF
ch.button.text.confirm=\u786E\u8BA4
ch.button.text.refresh=\u5237\u65B0
ch.button.text.button=\u9215
ch.button.text.lower=\u4F4E\u7EA7\u5B57\u7B26
ch.button.text.upper=\u4E0A\u90E8\u5B57\u7B26
ch.button.text.special=\u7279\u6B8A\u5B57\u7B26
ch.button.text.shift=\u8F6C\u79FB
ch.button.text.space=\u7A7A\u767D
ch.button.text.link=\u93C8\u63A5
