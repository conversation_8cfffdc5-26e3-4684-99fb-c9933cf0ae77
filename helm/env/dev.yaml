# Default values for corp-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

serverName: corp-api

image:
  repository: asia-northeast3-docker.pkg.dev/gowid-dev/gowid-repo/corp-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

service:
  type: ClusterIP
  port: 8081
  isLocal: false
  backendConfig: {"ports": {"8081":"corp-api"}}
  neg: {"ingress": true}

resources:
  limits:
    cpu: 0.4
    memory: 2048Mi
  requests:
    cpu: 0.4
    memory: 2048Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8081
  initialDelaySeconds: 150
  periodSeconds: 30
  failureThreshold: 1

lifecycle:
  preStop:
    exec:
      command:
        - /bin/sh
        - -c
        - sleep 60

terminationGracePeriodSeconds : 150
