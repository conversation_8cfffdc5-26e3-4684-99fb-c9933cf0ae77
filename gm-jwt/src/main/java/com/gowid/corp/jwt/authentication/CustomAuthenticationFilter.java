package com.gowid.corp.jwt.authentication;

import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.exception.NotFoundException;
import com.gowid.corp.core.repository.user.UserRepository;
import com.gowid.corp.jwt.dto.TokenDto;
import com.gowid.corp.jwt.exception.AccessTokenNotFoundException;
import com.gowid.corp.jwt.service.JwtService;
import io.jsonwebtoken.ExpiredJwtException;
import java.io.IOException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomAuthenticationFilter extends OncePerRequestFilter {

    private final UserRepository userRepository;
    private final UserDetailsService service;
    private final JwtService jwt;

    @Value("${expense.api-key}")
    private String expenseApiKey;

    @Value("${loan.api-key}")
    private String loanApiKey;

    @Value("${batch.api-key}")
    private String batchApiKey;

    private boolean isAPIKeyAuth(HttpServletRequest request) {
        final String apikey = request.getHeader(HttpHeaders.AUTHORIZATION);

        if (Strings.isEmpty(apikey)) {
            return false;
        }

        if (apikey.toLowerCase().startsWith("bearer")) {
            return false;
        }

        if (batchApiKey.equals(apikey)) {
            UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(request.getHeader(HttpHeaders.AUTHORIZATION), null, Lists.newArrayList());
            SecurityContextHolder.getContext().setAuthentication(token);
            return true;
        }
        return expenseApiKey.equals(apikey) || loanApiKey.equals(apikey);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        if (isAPIKeyAuth(request)) {
            chain.doFilter(request, response);
            return;
        }
        try {
            String bearerToken = request.getHeader(jwt.config().getHeader());
            if (!Strings.isEmpty(bearerToken)) {
                String token = jwt.fromBearerToken(bearerToken).orElseThrow(
                        () -> AccessTokenNotFoundException.builder()
                                .header(jwt.config().getHeader())
                                .bearerToken(bearerToken)
                                .build()
                );
                TokenDto jwtToken = this.jwt.parse(token);
                jwtToken.validAccessToken(token);

                UserDetails user;
                if (TokenDto.TokenType.JWT_ADMIN_ACCESS.equals(jwtToken.getTokenType())) {
                    User employeeUser = userRepository.findByEmail(jwtToken.getIdentifier()).orElseThrow(
                        () -> new NotFoundException("존재하지 않는 계정입니다.")
                    );
                    user = service.loadUserByUsername(employeeUser.email());
                } else {
                    user = service.loadUserByUsername(jwtToken.getIdentifier());
                }

                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (ExpiredJwtException e) {
            SecurityContextHolder.clearContext();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.warn("([ doFilterInternal ]) $error='Could not set user authentication in security context', $exception='{} => {}'",
                        e.getClass().getSimpleName(),
                        e.getMessage());
            }
            SecurityContextHolder.clearContext();
        }
        chain.doFilter(request, response);
    }

}
