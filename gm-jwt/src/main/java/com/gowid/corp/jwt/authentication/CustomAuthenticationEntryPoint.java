package com.gowid.corp.jwt.authentication;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.ResponseObject;
import com.gowid.corp.core.dto.response.ErrorCodeDescriptor;
import com.gowid.corp.core.dto.response.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
@SuppressWarnings({"unused", "SpellCheckingInspection"})
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

	private final ObjectMapper objmpr;

	public static class ErrorCode {
		enum Authentication implements ErrorCodeDescriptor {
			INVALID_TOKEN_USED,
		}
	}

	@Override
	public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
		log.error("Unauthorized Error : Request URI : {}, Request Header : {}", request.getRequestURI(), request.getHeader("authorization"));
		response.setStatus(response.SC_UNAUTHORIZED);
		response.setContentType(MediaType.APPLICATION_JSON_VALUE);
		response.setCharacterEncoding("UTF-8");
		GowidResponseV2<?> gowidResponse = new GowidResponseV2<>();
		gowidResponse.setResult(new ResponseObject(ResultCode.UNAUTHORIZED));
		response.getWriter().write(objmpr.writerWithDefaultPrettyPrinter().writeValueAsString(gowidResponse));
	}
}
