package com.gowid.corp.service;

import com.gowid.corp.abstracts.AbstractMockitoTest;
import com.gowid.corp.dto.BusinessClassificationDto;
import com.gowid.corp.core.domain.etc.BusinessClassification;
import com.gowid.corp.core.repository.etc.BusinessClassificationRepository;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.mockito.BDDMockito.given;
import static org.assertj.core.api.Assertions.assertThat;

public class BusinessClassificationServiceTest extends AbstractMockitoTest {
    @InjectMocks
    private BusinessClassificationService businessClassificationService;
    @Mock
    private BusinessClassificationRepository businessClassificationRepository;

    @Test
    public void ShouldReturnBusinessClassificationsConvertedToDtoFormat() {
        List<BusinessClassification> businessClassifications = new ArrayList<>();
        businessClassifications.add(BusinessClassification.builder().idx(1L).description("게임").build());
        businessClassifications.add(BusinessClassification.builder().idx(2L).description("광고/마케팅").build());
        businessClassifications.add(BusinessClassification.builder().idx(3L).description("교육").build());

        List<BusinessClassificationDto.Response> expected = businessClassifications.stream()
                .map(BusinessClassificationDto.Response::from)
                .collect(Collectors.toList());

        // given
        given(businessClassificationRepository.findAll()).willReturn(businessClassifications);

        // when
        List<BusinessClassificationDto.Response> actual = businessClassificationService.list();

        // then
        assertThat(actual).isEqualTo(expected);
    }
}
