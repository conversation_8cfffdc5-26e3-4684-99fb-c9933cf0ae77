package com.gowid.corp.service.lotte;

import com.gowid.corp.abstracts.AbstractSpringBootTest;
import com.gowid.corp.dto.CardIssuanceDto;
import com.gowid.corp.exception.EntityNotFoundException;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.lotte.FinancialConsumerProtectionActLotte;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.lotte.FinancialConsumerProtectionActLotteRepository;
import com.gowid.corp.core.domain.user.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Optional;

import static org.mockito.BDDMockito.given;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;


public class FinancialConsumerProtectionActLotteServiceTest extends AbstractSpringBootTest {
    @InjectMocks
    private FinancialConsumerProtectionActLotteService financialConsumerProtectionActLotteService;

    @Mock
    private CardIssuanceInfoRepository cardIssuanceInfoRepository;

    @Mock
    private FinancialConsumerProtectionActLotteRepository financialConsumerProtectionActLotteRepository;

    @Test
    @DisplayName("카드발급정보와 금소법 필요 데이터가 존재하면 금소법 엔티티가 반환된다.")
    public void Should_ReturnSavedEntity_When_ExistCardIssuanceInfoAndRequiredValuesByFinancialConsumer() {
        CardIssuanceDto.FinancialConsumerProtectionActReqByLotte request =
                CardIssuanceDto.FinancialConsumerProtectionActReqByLotte.builder()
                        .annualCompanyOperationIdentificationCode("1")
                        .financialConsumerIdentificationCode("1")
                        .generalFinancialConsumerRatingContents("1")
                        .totalCapitalAmountIdentificationCode("1")
                        .totalSalesIdentificationCode("1")
                        .build();
        User user = User.builder().idx(1L).build();
        CardIssuanceInfo cardIssuanceInfo = CardIssuanceInfo.builder().build();

        FinancialConsumerProtectionActLotte expected = FinancialConsumerProtectionActLotte.builder().build();

        given(cardIssuanceInfoRepository.findByUserAndCardCompany(user, CardCompany.LOTTE)).willReturn(Optional.ofNullable(cardIssuanceInfo));
        given(financialConsumerProtectionActLotteRepository.findByCardIssuanceInfo(cardIssuanceInfo)).willReturn(Optional.ofNullable(expected));
        given(financialConsumerProtectionActLotteRepository.save(expected)).willReturn(expected);

        FinancialConsumerProtectionActLotte actual = financialConsumerProtectionActLotteService.updateFinancialConsumerProtectionAct(user, request);

        assertThat(actual).isEqualTo(expected);
    }

    @Test
    @DisplayName("카드발급정보가 존재하지 않으면 예외가 발생한다.")
    public void Should_ThrowException_When_NotExistCardIssuanceInfo() {
        CardIssuanceDto.FinancialConsumerProtectionActReqByLotte request =
                CardIssuanceDto.FinancialConsumerProtectionActReqByLotte.builder()
                        .annualCompanyOperationIdentificationCode("1")
                        .financialConsumerIdentificationCode("1")
                        .generalFinancialConsumerRatingContents("1")
                        .totalCapitalAmountIdentificationCode("1")
                        .totalSalesIdentificationCode("1")
                        .build();
        User user = User.builder().idx(1L).build();

        given(cardIssuanceInfoRepository.findByUserAndCardCompany(user, CardCompany.LOTTE)).willThrow(EntityNotFoundException.class);

        assertThrows(EntityNotFoundException.class,
                () -> financialConsumerProtectionActLotteService.updateFinancialConsumerProtectionAct(user, request));
    }

    @Test
    @DisplayName("카드발급정보와 금소법 필요 데이터가 존재하면 금소법 엔티티가 반환된다.")
    public void Should_ReturnEntity_When_ExistCardIssuanceInfoAndRequiredValuesByFinancialConsumer() {
        CardIssuanceInfo cardIssuanceInfo = CardIssuanceInfo.builder().build();
        FinancialConsumerProtectionActLotte expected = FinancialConsumerProtectionActLotte.builder().build();
        given(cardIssuanceInfoRepository.findByIdx(1L)).willReturn(Optional.ofNullable(cardIssuanceInfo));
        given(financialConsumerProtectionActLotteRepository.findByCardIssuanceInfo(cardIssuanceInfo)).willReturn(Optional.ofNullable(expected));

        FinancialConsumerProtectionActLotte actual = financialConsumerProtectionActLotteService.getFinancialConsumerProtectionAct(1L);
        assertThat(actual).isEqualTo(expected);
    }
}
