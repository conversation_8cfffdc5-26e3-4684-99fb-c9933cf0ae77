package com.gowid.corp.service.shinhan;

import com.gowid.corp.abstracts.AbstractSpringBootTest;
import com.gowid.corp.dto.CardIssuanceDto;
import com.gowid.corp.exception.EntityNotFoundException;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.shinhan.FinancialConsumerProtectionActShinhanRepository;
import com.gowid.corp.core.domain.shinhan.FinancialConsumerProtectionActShinhan;
import com.gowid.corp.core.domain.user.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Optional;

import static org.mockito.BDDMockito.given;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class FinancialConsumerProtectionActShinhanServiceTest extends AbstractSpringBootTest {
    @InjectMocks
    private FinancialConsumerProtectionActShinhanService financialConsumerProtectionActShinhanService;

    @Mock
    private CardIssuanceInfoRepository cardIssuanceInfoRepository;

    @Mock
    private FinancialConsumerProtectionActShinhanRepository financialConsumerProtectionActShinhanRepository;

    @Test
    @DisplayName("카드발급정보와 금소법 필요 데이터가 존재하면 금소법 데이터를 업데이트 후 해당 엔티티가 반환된다.")
    public void Should_ReturnSavedEntity_When_ExistCardIssuanceInfoAndRequiredValuesByFinancialConsumer() {
        CardIssuanceDto.FinancialConsumerProtectionActReqByShinhan request =
                CardIssuanceDto.FinancialConsumerProtectionActReqByShinhan.builder()
                    .annualIncomeClassificationCode("1")
                    .annualSalesClassificationCode("1")
                    .creditScoreCode("1")
                    .creditRatingCode("1")
                    .isOverdueWithinThreeMonths(true)
                    .isOverdueWithinThreeMonthsByCorporation(false)
                    .isAgreeToOverdueTerms(true)
                    .isAgreeToEarlyTermination(true)
                    .isAgreeToLimitAdjustment(true)
                    .isAgreeToTheManual(true)
                    .isAgreeToCustomerRightsConsent(true)
                    .build();
        User user = User.builder().idx(1L).build();
        CardIssuanceInfo cardIssuanceInfo = CardIssuanceInfo.builder().build();
        FinancialConsumerProtectionActShinhan expected =
                FinancialConsumerProtectionActShinhan.builder()
                        .annualIncomeClassificationCode("1")
                        .annualSalesClassificationCode("1")
                        .creditScoreCode("1")
                        .creditRatingCode("1")
                        .isOverdueWithinThreeMonths(true)
                        .isOverdueWithinThreeMonthsByCorporation(false)
                        .isAgreeToOverdueTerms(true)
                        .isAgreeToEarlyTermination(true)
                        .isAgreeToLimitAdjustment(true)
                        .isAgreeToTheManual(true)
                        .isAgreeToCustomerRightsConsent(true)
                        .build();

        given(cardIssuanceInfoRepository.findByUserAndCardCompanyAndCardType(user, CardCompany.SHINHAN, CardType.GOWID))
                .willReturn(Optional.ofNullable(cardIssuanceInfo));
        given(financialConsumerProtectionActShinhanRepository.findByCardIssuanceInfo(cardIssuanceInfo))
                .willReturn(Optional.ofNullable(expected));
        given(financialConsumerProtectionActShinhanRepository.save(expected)).willReturn(expected);

        FinancialConsumerProtectionActShinhan actual =
                financialConsumerProtectionActShinhanService.updateFinancialConsumerProtectionAct(user, CardType.GOWID, request);

        assertThat(actual).isEqualTo(expected);
    }

    @Test
    @DisplayName("카드발급정보가 존재하지 않으면 예외가 발생한다.")
    public void Should_ThrowException_When_NotExistCardIssuanceInfo() {
        CardIssuanceDto.FinancialConsumerProtectionActReqByShinhan request =
                CardIssuanceDto.FinancialConsumerProtectionActReqByShinhan.builder()
                        .annualIncomeClassificationCode("1")
                        .annualSalesClassificationCode("1")
                        .creditScoreCode("1")
                        .creditRatingCode("1")
                        .isOverdueWithinThreeMonths(true)
                        .isOverdueWithinThreeMonthsByCorporation(false)
                        .isAgreeToOverdueTerms(true)
                        .isAgreeToEarlyTermination(true)
                        .isAgreeToLimitAdjustment(true)
                        .isAgreeToTheManual(true)
                        .isAgreeToCustomerRightsConsent(true)
                        .build();
        User user = User.builder().idx(1L).build();

        given(cardIssuanceInfoRepository.findByUserAndCardCompanyAndCardType(user, CardCompany.SHINHAN, CardType.GOWID)).willThrow(EntityNotFoundException.class);

        assertThrows(EntityNotFoundException.class,
                () -> financialConsumerProtectionActShinhanService.updateFinancialConsumerProtectionAct(user, CardType.GOWID, request));
    }

    @Test
    @DisplayName("카드발급정보와 금소법 필요 데이터가 존재하면 금소법 엔티티가 반환된다.")
    public void Should_ReturnEntity_When_ExistCardIssuanceInfoAndRequiredValuesByFinancialConsumer() {
        CardIssuanceInfo cardIssuanceInfo = CardIssuanceInfo.builder().build();
        FinancialConsumerProtectionActShinhan expected = FinancialConsumerProtectionActShinhan.builder().build();

        given(cardIssuanceInfoRepository.findByIdx(1L)).willReturn(Optional.ofNullable(cardIssuanceInfo));
        given(financialConsumerProtectionActShinhanRepository.findByCardIssuanceInfo(cardIssuanceInfo)).willReturn(Optional.ofNullable(expected));

        FinancialConsumerProtectionActShinhan actual = financialConsumerProtectionActShinhanService.getFinancialConsumerProtectionAct(1L);
        assertThat(actual).isEqualTo(expected);
    }
}
