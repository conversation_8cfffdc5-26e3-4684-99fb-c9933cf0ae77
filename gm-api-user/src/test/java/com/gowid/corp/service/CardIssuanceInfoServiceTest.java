package com.gowid.corp.service;

import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.domain.user.User;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CardIssuanceInfoServiceTest {

	@InjectMocks
	private CardIssuanceInfoService cardIssuanceInfoService;

	@Mock
	private CardIssuanceInfoRepository cardIssuanceInfoRepository;

	@Test
	@DisplayName("신청정보를 찾을 수 없을 때 NULL을 응답해야 한다")
	void Should_null_When_cardIssuanceInfoIsNull() {
		// given
		User user = User.builder().build();
		CardType cardType = CardType.GOWID;

		// when
		when(cardIssuanceInfoRepository.findByUserAndCardType(user, cardType)).thenReturn(Optional.empty());
		CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findByUserOrNull(user, cardType);

		// then
		Assertions.assertNull(cardIssuanceInfo);
	}

	@Test
	@DisplayName("신청정보가 있다면 신청정보를 응답해야 한다")
	void Should_cardIssuanceInfo_When_cardIssuanceInfoIsPresent() {
		// given
		User user = User.builder().build();
		CardType cardType = CardType.GOWID;
		CardIssuanceInfo expected = CardIssuanceInfo.builder().idx(1L).build();

		// when
		when(cardIssuanceInfoRepository.findByUserAndCardType(user, cardType)).thenReturn(Optional.ofNullable(expected));
		CardIssuanceInfo actual = cardIssuanceInfoService.findByUserOrNull(user, cardType);

		// then
		Assertions.assertNotNull(actual);
		Assertions.assertEquals(expected.idx(), actual.idx());
	}
}
