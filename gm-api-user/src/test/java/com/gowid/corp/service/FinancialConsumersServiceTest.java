package com.gowid.corp.service;

import com.gowid.corp.abstracts.AbstractSpringBootTest;
import com.gowid.corp.dto.FinancialConsumersResponseDto;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.domain.user.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

class FinancialConsumersServiceTest extends AbstractSpringBootTest {

	@Autowired
	private FinancialConsumersService financialConsumersService;
	@Autowired
	private UserService userService;

	@Test
	@Transactional
	@DisplayName("상시근로 5인이상 여부 필드가 업데이트 된다")
	public void shouldUpdateOverFiveEmployees(){
		User user = userService.findByEmail("<EMAIL>");
		boolean overFiveEmployees = true;

		FinancialConsumersResponseDto responseDto
			= financialConsumersService.updateOverFiveEmployees(user, CardType.GOWID, overFiveEmployees);

		assertThat(responseDto.getOverFiveEmployees()).isEqualTo(overFiveEmployees);
	}

}
