package com.gowid.corp.domain.address;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;

@DisplayName("법인 정보: 주소 테스트")
public class AddressTest {
    @DisplayName("사용자 주소를 첫 번째 쉼표를 기준으로 분리하여 저장하면 기본 주소만 가져올 수 있다.")
    @ParameterizedTest
    @CsvSource(value = { "인천광역시 연수구 갯벌로 12, 7-16호(송도동, 인천창조경제혁신센터 송도본원):인천광역시 연수구 갯벌로 12",
            "경기도 평택시 산단로 263, 사조물류창고 1층(칠괴동):경기도 평택시 산단로 263",
            "서울특별시 강남구 논현로99길 23, 지상1층 2호실(역삼동):서울특별시 강남구 논현로99길 23" }, delimiter = ':')
    void Should_getBasicAddress_When_splitDelimiter(String userAddr, String expected) {
        Address address = new Address(userAddr);
        assertEquals(expected, address.getAddress());
    }

    @DisplayName("사용자 주소를 첫 번째 쉼표를 기준으로 분리하여 저장하면 상세 주소만 가져올 수 있다.")
    @ParameterizedTest
    @CsvSource(value = { "인천광역시 연수구 갯벌로 12, 7-16호(송도동, 인천창조경제혁신센터 송도본원):7-16호(송도동, 인천창조경제혁신센터 송도본원)",
            "경기도 평택시 산단로 263, 사조물류창고 1층(칠괴동):사조물류창고 1층(칠괴동)",
            "서울특별시 강남구 논현로99길 23, 지상1층 2호실(역삼동):지상1층 2호실(역삼동)" }, delimiter = ':')
    void Should_getDetailedAddress_When_splitDelimiter(String userAddr, String expected) {
        Address address = new Address(userAddr);
        assertEquals(expected, address.getAddressDetail());
    }

    @DisplayName("상세 주소가 없는 경우 상세 주소는 null로 저장된다.")
    @ParameterizedTest
    @ValueSource(strings = {"경상북도 상주시 냉림3길 38(냉림동)", "광주광역시 광산구 평동산단7번로 82(연산동)", "경기도 김포시 통진읍 김포대로 2056-29"})
    void Should_getNull_When_DetailedAddressIsNull(String userAddr) {
        Address address = new Address(userAddr);

        assertAll(
                () -> assertEquals(userAddr, address.getAddress()),
                () -> assertNull(address.getAddressDetail())
        );
    }

    @DisplayName("사용자 주소의 값이 빈 값이거나 null이면 에러가 발생한다.")
    @ParameterizedTest
    @NullAndEmptySource
    void Should_throwGowidException_When_UserAddressIsNullOrEmpty(String userAddr) {
        assertThatThrownBy(
                () -> new Address(userAddr)
        ).isInstanceOf(GowidException.class)
                .hasMessage(ResultCode.USER_ADDRESS_IS_REQUIRED.getDesc());
    }
}
