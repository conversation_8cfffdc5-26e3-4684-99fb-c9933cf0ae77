package com.gowid.corp.fixtures;

import com.gowid.corp.v2.dto.StocksReqDto;

import java.time.LocalDate;

public class StockReqDtoFixture {

    public static StocksReqDto createSuccessfulStocksReqDto() {
        final LocalDate today = LocalDate.now();
        return StocksReqDto.of("261-81-25793", today.minusDays(1), today);
    }

    public static StocksReqDto createNullDateStocksReqDto() {
        return StocksReqDto.of("261-81-25793", null, null);
    }

    public static StocksReqDto createWrongDateStocksReqDto() {
        final LocalDate today = LocalDate.now();
        return StocksReqDto.of("261-81-25793", today, today.minusDays(1));
    }
}
