package com.gowid.corp.controller;

import com.gowid.corp.abstracts.AbstractWebMvcTest;
import com.gowid.corp.dto.AccountDto;
import com.gowid.corp.dto.EmailCheckResponseDto;
import com.gowid.corp.service.AuthService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class AuthControllerTest extends AbstractWebMvcTest {

    @MockBean
    private AuthService authService;

    @Test
    void emailCheck_shouldReturnOkWithExistingEmail() throws Exception {
        // Given
        String email = "<EMAIL>";
        EmailCheckResponseDto responseDto = new EmailCheckResponseDto(true);
        when(authService.isAlreadyExistUser(email)).thenReturn(responseDto);

        // When & Then
        mockMvc.perform(
                get("/auth/v1/exists").param("account", email)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.exists").value(true));
    }

    @Test
    void getAccount_shouldReturnAccountList() throws Exception {
        // Given
        String name = "John Doe";
        String mdn = "***********";
        List<String> accounts = Arrays.asList("<EMAIL>", "<EMAIL>");
        when(authService.findAccount(name, mdn)).thenReturn(accounts);

        // When & Then
        mockMvc.perform(
                get("/auth/v1/account")
                    .param("name", name)
                    .param("mdn", mdn)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0]").value("<EMAIL>"))
            .andExpect(jsonPath("$[1]").value("<EMAIL>"));
    }

    @Test
    void reissueAccessToken_shouldReturnTokenSet() throws Exception {
        // Given
        AccountDto accountDto = AccountDto.builder()
            .email("<EMAIL>")
            .refreshToken("refresh_token")
            .build();

        // When & Then
        mockMvc.perform(
                post("/auth/v1/token/reissue")
                    .content(json(accountDto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void getAuthInfo_shouldReturnUserInfo() throws Exception {
        // Given
        String token = getToken();

        // When & Then
        mockMvc.perform(
                get("/auth/v1/info")
                    .header("Authorization", "Bearer " + token)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }
}
