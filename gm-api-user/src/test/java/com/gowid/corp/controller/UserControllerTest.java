package com.gowid.corp.controller;

import com.gowid.corp.abstracts.AbstractWebMvcTest;
import com.gowid.corp.dto.CorpDto;
import com.gowid.corp.dto.SignUpSeedReqDto;
import com.gowid.corp.dto.SignUpSeedResDto;
import com.gowid.corp.dto.UserDto;
import com.gowid.corp.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class UserControllerTest extends AbstractWebMvcTest {

    @MockBean
    private UserService userService;

    @Test
    void getUserInfo_shouldReturnUserInfo() throws Exception {
        // Given
        String token = getToken();

        // When & Then
        mockMvc.perform(
                get("/user/v1/info")
                    .header("Authorization", "Bearer " + token)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void registerBrandUser_shouldRegisterUser() throws Exception {
        // Given
        UserDto.RegisterBrandUser dto = new UserDto.RegisterBrandUser();
        // Set necessary fields in dto

        // When & Then
        mockMvc.perform(
                post("/user/v1/registration/user")
                    .content(json(dto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void getBrandCorp_shouldReturnCorpInfo() throws Exception {
        // Given
        String token = getToken();

        // When & Then
        mockMvc.perform(
                get("/user/v1/registration/corp")
                    .header("Authorization", "Bearer " + token)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void getBrandCorpBranch_shouldReturnCorpBranchInfo() throws Exception {
        // Given
        String token = getToken();
        CorpDto.CorpInfoDto corpInfoDto = new CorpDto.CorpInfoDto();
        when(userService.getBrandCorpBranch(anyLong())).thenReturn(corpInfoDto);

        // When & Then
        mockMvc.perform(
                get("/user/v1/registration/corp-branch")
                    .header("Authorization", "Bearer " + token)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void registerUserUpdate_shouldUpdateUserInfo() throws Exception {
        // Given
        String token = getToken();
        UserDto.registerUserUpdate dto = new UserDto.registerUserUpdate();
        // Set necessary fields in dto

        // When & Then
        mockMvc.perform(
                post("/user/v1/registration/info")
                    .header("Authorization", "Bearer " + token)
                    .content(json(dto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void limitReview_shouldRequestLimitReview() throws Exception {
        // Given
        String token = getToken();
        UserDto.LimitReview dto = new UserDto.LimitReview();
        // Set necessary fields in dto

        // When & Then
        mockMvc.perform(
                post("/user/v1/limit-review")
                    .header("Authorization", "Bearer " + token)
                    .content(json(dto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void requestDeleteAccount_shouldRequestAccountDeletion() throws Exception {
        // Given
        String token = getToken();
        UserDto.DeleteUserAccount dto = new UserDto.DeleteUserAccount();
        // Set necessary fields in dto

        // When & Then
        mockMvc.perform(
                post("/user/v1/delete-account")
                    .header("Authorization", "Bearer " + token)
                    .content(json(dto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }

    @Test
    void getUserEmailViaToken_shouldReturnUserEmail() throws Exception {
        // Given
        String token = getToken();
        when(userService.getUserEmailViaToken(anyString())).thenReturn("<EMAIL>");

        // When & Then
        mockMvc.perform(
                get("/user/v1/tokens")
                    .header("Authorization", "Bearer " + token)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").value("<EMAIL>"));
    }

    @Test
    void validateApplication_shouldValidateApplication() throws Exception {
        // Given
        String email = "<EMAIL>";
        when(userService.isPossibleCardApplication(email)).thenReturn(true);

        // When & Then
        mockMvc.perform(
                get("/user/v1/application/validate/" + email)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void verifySeed_shouldVerifySeed() throws Exception {
        // Given
        // Using reflection to set the seed field since there's no setter
        SignUpSeedReqDto reqDto = new SignUpSeedReqDto();
        try {
            java.lang.reflect.Field seedField = SignUpSeedReqDto.class.getDeclaredField("seed");
            seedField.setAccessible(true);
            seedField.set(reqDto, "test-seed");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        SignUpSeedResDto resDto = new SignUpSeedResDto("<EMAIL>", "Test User", "01012345678", "Test Corp");
        when(userService.verifySeed(anyString())).thenReturn(resDto);

        // When & Then
        mockMvc.perform(
                post("/user/v1/verify/registration/seed")
                    .content(json(reqDto))
                    .contentType(MediaType.APPLICATION_JSON)
                    .characterEncoding("UTF-8")
            )
            .andDo(print())
            .andExpect(status().isOk());
    }
}
