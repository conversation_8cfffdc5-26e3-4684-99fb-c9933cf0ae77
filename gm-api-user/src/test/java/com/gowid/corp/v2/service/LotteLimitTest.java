package com.gowid.corp.v2.service;


import com.gowid.corp.abstracts.AbstractSpringBootTest;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceStatus;
import com.gowid.corp.core.domain.lotte.Lotte_D1100;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.lotte.Lotte_D1100Repository;
import com.gowid.corp.core.exception.NotFoundException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.assertEquals;

class LotteLimitTest extends AbstractSpringBootTest {

    @Autowired
    CardIssuanceInfoRepository cardIssuanceInfoRepository;
    @Autowired
    Lotte_D1100Repository lotteD1100Repository;

    @DisplayName("발급 정보 여러개 중 최신")
    @Test
    @Transactional
    void findTest() {
        // given
        String resCompanyIdentityNo = "261-81-25793";
        IssuanceStatus ISSUED = IssuanceStatus.ISSUED;
        Long cardIssuanceInfoIdx = 1275L;

        // when
        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoRepository.findTopByCorpResCompanyIdentityNoOrderByUpdatedAtDesc(resCompanyIdentityNo).orElseThrow(NotFoundException::new);

        // then
        assertEquals(cardIssuanceInfo.idx(), cardIssuanceInfoIdx);
        assertEquals(cardIssuanceInfo.issuanceStatus(), ISSUED);
        assertEquals(cardIssuanceInfo.corp().resCompanyIdentityNo(), resCompanyIdentityNo);
    }

    @DisplayName("최신 LotteD1100 전문 조회")
    @Test
    void selectLotteD1100Table() {
        // given
        long idxCorp = 1088L;

        // when
        Lotte_D1100 lotteD1100 = lotteD1100Repository.findTopByIdxCorpOrderByUpdatedAtDesc(idxCorp).orElseThrow(NotFoundException::new);
        String akLim = lotteD1100.getAkLimAm();

        // then
        assertEquals("2000", akLim);
    }

    @DisplayName("최신 LotteD1100 전문 akLimAm 업데이트")
    @Transactional
    @Test
    void updateLotteD1100Table() {
        // given
        long idxCorp = 1088L;
        String changedLimit = "2000";

        Lotte_D1100 lotteD1100 = lotteD1100Repository.findTopByIdxCorpOrderByUpdatedAtDesc(idxCorp).orElseThrow(NotFoundException::new);

        // when
        lotteD1100.setAkLimAm(changedLimit);
        lotteD1100Repository.save(lotteD1100);

        // then
        Lotte_D1100 afterLotteD1100 = lotteD1100Repository.findTopByIdxCorpOrderByUpdatedAtDesc(idxCorp).orElseThrow(NotFoundException::new);
        assertEquals("2000", afterLotteD1100.getAkLimAm());
    }
}

