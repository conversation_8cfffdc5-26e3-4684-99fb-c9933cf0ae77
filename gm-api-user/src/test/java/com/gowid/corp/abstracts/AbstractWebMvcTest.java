package com.gowid.corp.abstracts;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gowid.corp.UserApiApplication;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.AccountDto;
import com.gowid.corp.service.UserService;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.web.context.WebApplicationContext;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

@Slf4j
@AutoConfigureMockMvc
@ActiveProfiles("test")
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = UserApiApplication.class)
public abstract class AbstractWebMvcTest {

	protected MockMvc mockMvc;
	private User user;
	protected CustomUser customUser;
	private ObjectMapper mapper = new ObjectMapper();

	@Autowired
	private WebApplicationContext webApplicationContext;

	@Autowired
	private UserService userService;

	@BeforeEach
	public void setup() {
		mockMvc = webAppContextSetup(webApplicationContext).apply(springSecurity()).build();
		user = userService.getByIdxOrThrow(487L);
		customUser = new CustomUser(user);
	}

	protected String getToken() throws Exception {
		AccountDto account = AccountDto.builder().email("<EMAIL>").password("1234").build();
		return extractToken(login(account).andReturn());
	}


	protected String getToken(String email, String password) throws Exception {
		AccountDto account = AccountDto.builder().email(email).password(password).build();
		return extractToken(login(account).andReturn());
	}

	protected ResultActions login(AccountDto dto) throws Exception {

		return mockMvc.perform(
			post("/auth/v2/token/issue")
				.content(json(dto))
				.contentType(MediaType.APPLICATION_JSON));
	}

	protected String json(Object o) throws IOException {
		return mapper.writeValueAsString(o);
	}

	protected String extractToken(MvcResult result) throws UnsupportedEncodingException {
		return JsonPath.read(result.getResponse().getContentAsString(), "$.jwtAccess");
	}

}
