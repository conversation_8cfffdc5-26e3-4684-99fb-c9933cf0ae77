spring:
  profiles:
    active: junit, dev, mobile
    include:
      - jwt
      - core-stage
      - redis-stage
      - resx
  devtools:
    livereload:
      enabled: true
  freemarker:
    cache: false
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB

server:
  port: 8080
  error:
    include-stacktrace: never
  tomcat:
    additional-tld-skip-patterns: "*.jar"

logging:
  level:
    root: info

gateway:
  shinhan:
    uri:
      1200: /shinhan/d1200
      3000: /shinhan/d3000
      1000: /shinhan/d1000
      1400: /shinhan/d1400
      1510: /shinhan/d1510
      1520: /shinhan/d1520
      1530: /shinhan/d1530
      1100: /shinhan/d1100
      1700: /shinhan/d1700
      1800: /shinhan/d1800
      bpr-transfer: /shinhan/transfer/images/
      bpr-single-transfer: /shinhan/transfer/image/
  lotte:
    uri:
      1300: /lotte/d1300
      1200: /lotte/d1200
      1100: /lotte/d1100
      1000: /lotte/d1000
      image-zip: /lotte/image/zip
      image-send: /lotte/image/send

stibee:
  address-book-url: https://api.stibee.com/v1/lists/@addressBookId/subscribers
  accessToken: 0cb71d0c66b14b96402b5a3f1f3095e905cd01609796900884789d2b2ecc9949af3b1c732a7c39f1eec95f2392e0a8a8fb442631061ac62197a0f28a4bbc9bad
  email-template:
    email-test:
      template-url: https://stibee.com/api/v1.0/auto/NjQ5MzcwOGUtNjhmNS00NDhhLWI3NGMtODMwYWY4MzFmYjI4
      address-book-id: 128086
    saas-welcome-mail:
      template-url: https://stibee.com/api/v1.0/auto/MTEyMGUwZWUtYjk0Ny00Mjc0LWFlMWEtZmJhMzZlZTA5Njgx
      address-book-id: 129980
    re-registration-certificate:
      template-url: https://stibee.com/api/v1.0/auto/Y2Q2MDM5NzUtMTY5Mi00ZDk0LWFjMjItYmQyOTI1MWUyZGNh
      address-book-id: 132190
    expired-certificate:
      template-url: https://stibee.com/api/v1.0/auto/NWM2NDdiMTctZmMwNC00ZmRhLTlmYzQtZmY5ZTIzZTlhZTA2
      address-book-id: 133469
    notice-payment-date:
      template-url: https://stibee.com/api/v1.0/auto/NjQ2ZGE1MGEtNTQ0OC00ZTZiLWI4MDctYWJkMjk1ZDM1NDQz
      address-book-id: 133470
    lotte-card-approval:
      template-url: https://stibee.com/api/v1.0/auto/ODIxMzI0M2ItM2NiMS00Y2JjLWE1NGItMGRkM2FlZjNlZGE3
      address-book-id: 149855
    shinhan-card-approval:
      template-url: https://stibee.com/api/v1.0/auto/NDIwMDFkYmEtZWVhNi00ZjI1LThhZDUtNWJhOTI0ZGY2M2I0
      address-book-id: 158922
    lotte-lock-in-3days:
      template-url: https://stibee.com/api/v1.0/auto/MTc3YTNhMGEtMzQ0MS00MjY5LTkyMTItMGZjMmViMmIzYjA4
      address-book-id: 149855
    lotte-lock-in-10days:
      template-url: https://stibee.com/api/v1.0/auto/YmVmMDYwNTktYzc2NC00MzczLTg4MjAtZDUzNjc4YWNkZGQy
      address-book-id: 149855
    shinhan-lock-in-3days:
      template-url: https://stibee.com/api/v1.0/auto/ZTIxYzYwMjAtNzNkNC00YzBmLWEwYzgtZjM5MTQ1MjU1NDNk
      address-book-id: 158922
    shinhan-lock-in-10days:
      template-url: https://stibee.com/api/v1.0/auto/ZTlkYTRhYjQtNzc2MC00MTExLWE2OTQtOWI4MWI1YTE4NzNm
      address-book-id: 158922

feign:
  client:
    config:
      default:
        connectTimeout: 20000
        readTimeout: 200000
    default-to-properties: false

salesforce:
  enabled: false

---

spring:
  profiles: test
  mail:
    sender: gowid <<EMAIL>>
    riskteam: gowid <<EMAIL>>
    host: email-smtp.ap-northeast-2.amazonaws.com
    port: 587
    username: AKIAXD7XVDFHORA6NRP3
    password: BIS4ljrn5kHAy8wFJjwJD5SC6wh9orlIk57Wbq7gFYZs
    #    host: smtp.gmail.com
    #    port: 587
    #    username: <EMAIL>
    #    password: kfiqihdulvgkhphj
    protocol: smtp
    properties:
      mail:
        debug: true
        smtp:
          starttls:
            enable: true
          auth: true
    default-encoding: UTF-8
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      use-new-id-generator-mappings: true
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true
    show-sql: true
    generate-ddl: true
    open-in-view: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      max-lifetime: 540000
      maximum-pool-size: 21
    initialization-mode: always
    url: **************************************************************************************
    username: gowidapi
    password: HR9x6L&k*c

tomcat:
  ajp:
    protocol: AJP/1.3
    port: 8009
    enabled: true
    connection-timeout: 60000

gateway:
  idc:
    shinhan: http://************:8080
    lotte: http://************:8090
#  idc:
#    host: ************:8080
#    protocol: http

expense:
  domain-url: https://stg-expense-auth.gowid.com
  user-url: /user
  status-url: /corporation
  corporation-url: /corporation
  api-key: expapisvR2021!
  access-key: 94d5e8d4-92a3-11eb-a8b3-0242ac130003

loan:
  api-key: 0CE25301871C6CDA95BC5918286D5284

encryption:
  keypad:
    enable: true
  seed128:
    enable: true

stockholder:
  file:
    size: 2097152
    type: jpg, jpeg, tif, tiff

mail:
  approved:
    send-enable: true
  receipt:
    send-enable: false
  invite-link-url: https://staging.gowid.com/invitation_mail

nhn-cloud:
  sms-send-url: https://api-sms.cloud.toast.com/sms/v2.4/appKeys/@appKey/sender/mms
  app-key: hQY8nWYdqaxp9dTb
  sms-templates:
    sms-sample: template-dev-01

sentry:
  dsn: https://<EMAIL>/5394758
  enabled: true

keypad.nprotect.properties.path: resource/nprotect.properties

feign:
  service:
    expense:
      url: http://************:8070
      api-key: scrapingsvr!@
---
