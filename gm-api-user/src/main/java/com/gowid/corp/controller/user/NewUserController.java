package com.gowid.corp.controller.user;

import com.gowid.corp.controller.AbstractController;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.dto.user.RegistrationUserDto;
import com.gowid.corp.dto.user.UserApplicationInfoDto;
import com.gowid.corp.jwt.dto.TokenDto;
import com.gowid.corp.service.UserService;
import com.gowid.corp.service.user.NewUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(NewUserController.URI.BASE)
@Tag(name = "[001] New User Controller", description = "Controller for handling new user operations")
@RequiredArgsConstructor
public class NewUserController extends AbstractController {
    public static class URI {
        public static final String BASE = "/user/v2";
        public static final String CHECK_REGISTRATION = "/check/registration";
        public static final String CHECK_PHONE_NUMBER = "/check/phoneNumber";
        public static final String REGISTRATION_USER = "/registration/user";
        public static final String CHECK_REDIS_KEY = "/check-redis-key";
    }

    private final NewUserService newUserService;

    @Operation(summary = "사업자 등록번호 체크")
    @GetMapping(URI.CHECK_REGISTRATION)
    public GowidResponseV2<Boolean> checkRegistrationNumber(
            @RequestParam("registrationNumber") String registrationNumber
    ) {
        log.info("-- REGISTRATION NUMBER CHECK --");
        log.info("-- REGISTRATION NUMBER : {}", registrationNumber);
        return ok(newUserService.checkRegistrationNumber(registrationNumber));
    }

    @Operation(summary = "전화번호 검증")
    @GetMapping(URI.CHECK_PHONE_NUMBER)
    public GowidResponseV2<Void> checkPhoneNumber(
            @RequestParam("phoneNumber") String phoneNumber,
            @RequestParam("region") String region
    ) {
        log.info("checkPhoneNumber -- {}", phoneNumber);
        log.info("checkRegion -- {}", region);
        newUserService.checkPhoneNumber(phoneNumber,region);
        return ok();
    }

    @Operation(summary = "신규 사용자 등록 & Corp 등록 & Corp Detail 초기 데이터 생성")
    @PostMapping(URI.REGISTRATION_USER)
    public GowidResponseV2<TokenDto.TokenSet> registerUserAndCorp(
            @RequestBody RegistrationUserDto param
    ) {
        log.info("registerUserAndCorp -- {}", param.toString());
        return ok(newUserService.registerUserAndCorp(param));
    }

    @Operation(summary = "도입 신청서 입력 정보 조회")
    @GetMapping(URI.CHECK_REDIS_KEY)
    public GowidResponseV2<UserApplicationInfoDto> checkRedisKey(
            @RequestParam("key") String key
    ) {
        log.info("checkRedisKey -- {}", key);
        return ok(newUserService.getUserApplicationInfo(key));
    }

}
