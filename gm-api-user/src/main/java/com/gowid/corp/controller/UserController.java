package com.gowid.corp.controller;

import com.gowid.corp.core.annotation.CurrentUser;
import com.gowid.corp.core.dto.GowidResponse;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.BusinessResponse;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.CorpDto;
import com.gowid.corp.dto.SignUpSeedReqDto;
import com.gowid.corp.dto.SignUpSeedResDto;
import com.gowid.corp.dto.UserDto;
import com.gowid.corp.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@Slf4j

@RestController
@RequestMapping(UserController.URI.BASE)
@RequiredArgsConstructor
@Validated
@Tag(name = "[02] 사용자", description = UserController.URI.BASE)
public class UserController extends AbstractController {

	public static class URI {
		public static final String BASE = "/user/v1";
		public static final String REGISTRATION_USER = "/registration/user"; // 유저등록
		public static final String REGISTRATION_CORP = "/registration/corp"; // 가능 한도 확인, 결제계좌 선택??
		public static final String REGISTRATION_INFO = "/registration/info"; //
		public static final String REGISTRATION_CORP_BRANCH = "/registration/corp-branch";
		public static final String REGISTRATION_CONSENT = "/registration/consent"; // 약관동의, 카드관리자등록
		public static final String LIMIT_REVIEW = "/limit-review";
		public static final String DELETE_ACCOUNT = "/delete-account";
		public static final String INFO = "/info";
	}

	private final UserService service;

	//==================================================================================================================
	//
	//	정보 조회
	//
	//==================================================================================================================

	@Operation(summary = "정보조회(요청하는 사용자의 기본정보를 반환)")
	@GetMapping(URI.INFO)
	public UserDto getUserInfo(
			@Parameter(hidden = true) @CurrentUser CustomUser user
	) {
		if (log.isInfoEnabled()) {
			log.info("([ getUserInfo ]) $user='{}'", user);
		}
		return service.getUserInfo(user.idx());
	}

	//==================================================================================================================
	//
	//	사용자 등록(회원가입): 선택약관 수신동의 여부, 이메일, 비밀번호, 이름, 연락처
	//
	//==================================================================================================================
	@Operation(
			summary = "사용자 회원가입",
			description = "### Remarks \n - <mark>액세스토큰 불필요</mark>"
	)
	@ApiResponses(value={
			@ApiResponse(responseCode = "200", description = "정상"),
			@ApiResponse(responseCode = "201", description = "생성"),
			@ApiResponse(responseCode = "401", description = "권한없음(패스워드 불일치)"),
			@ApiResponse(responseCode = "403", description = "권한없음(패스워드 불일치)"),
			@ApiResponse(responseCode = "404", description = "등록되지 않은 이메일"),
			@ApiResponse(responseCode = "500", description = "")
	})
	@PostMapping(URI.REGISTRATION_USER)
	public ResponseEntity<?> registerBrandUser(
			@RequestBody UserDto.RegisterBrandUser dto) {
		if (log.isInfoEnabled()) {
			log.info("([ registerBrandUser ]) $dto='{}'", dto);
		}

		return service.registerBrandUser(dto);
	}

	@Deprecated
	@Operation(summary = "Brand 회원가입 법인정보")
	@PostMapping(path = URI.REGISTRATION_CORP, consumes = {MediaType.APPLICATION_JSON_VALUE})
	public ResponseEntity<?> registerBrandCorp(
			@Parameter(hidden = true) @CurrentUser CustomUser user,
			@RequestBody UserDto.RegisterBrandCorp dto
	) {
		if (log.isInfoEnabled()) {
			log.info("([ registerBrandCorp ]) $user='{}' $dto='{}'", user, dto);
		}

		return service.registerBrandCorp(user.idx(), dto);
	}

	@Operation(summary = "Brand 회원가입 법인정보")
	@GetMapping(path = URI.REGISTRATION_CORP)
	public ResponseEntity<?> getBrandCorp(
			@Parameter(hidden = true) @CurrentUser CustomUser user
	) {
		if (log.isInfoEnabled()) {
			log.info("([ getBrandCorp ]) $user='{}'", user);
		}

		if(user == null){
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);
		}
 
		return service.getBrandCorp(user.idx()); 
	}

	@PreAuthorize("hasAnyAuthority('ROLE_MASTER, ROLE_VIEWER, ROLE_EXPENSE_MANAGER')")
	@Operation(summary = "Brand 회원가입 법인정보 추가정보")
	@GetMapping(path = URI.REGISTRATION_CORP_BRANCH)
	public GowidResponse<CorpDto.CorpInfoDto> getBrandCorpBranch(@Parameter(hidden = true) @CurrentUser CustomUser user) {
		return okV1(service.getBrandCorpBranch(user.idx()));
	}

	@Operation(summary = "Brand 내 정보 수정")
	@ApiResponses(value={
			@ApiResponse(responseCode = "200", description = "정상"),
			@ApiResponse(responseCode = "201", description = "생성"),
			@ApiResponse(responseCode = "401", description = "권한없음(패스워드 불일치)"),
			@ApiResponse(responseCode = "403", description = "권한없음(패스워드 불일치)"),
			@ApiResponse(responseCode = "404", description = "등록되지 않은 이메일"),
			@ApiResponse(responseCode = "500", description = "")
	})
	@PostMapping(URI.REGISTRATION_INFO)
	public ResponseEntity<?> registerUserUpdate(
			@Parameter(hidden = true) @CurrentUser CustomUser user,
			@RequestBody UserDto.registerUserUpdate dto
	) {
		if (log.isInfoEnabled()) {
			log.info("([ registerUserUpdate ]) $user='{}' $dto='{}'", user, dto);
		}

		return service.registerUserUpdate(dto, user.idx());
	}

	@Operation(summary = "사용자별 이용약관 등록")
	@PostMapping(URI.REGISTRATION_CONSENT)
	@Deprecated
	public ResponseEntity<?> registerUserConsent(
			@Parameter(hidden = true) @CurrentUser CustomUser user,
			@RequestBody UserDto.RegisterUserConsent dto
	) {

		return ResponseEntity.ok().body(
				BusinessResponse.builder().
						data(service.registerUserConsentLegacy(dto, user.idx()))
						.build());
	}

	@Operation(summary = "한도 재심사 요청")
	@PostMapping(URI.LIMIT_REVIEW)
	public ResponseEntity<?> limitReview(
			@Parameter(hidden = true) @CurrentUser CustomUser user,
			@RequestBody UserDto.LimitReview dto
	) {
		if (log.isInfoEnabled()) {
			log.info("([ limitReview ]) $user='{}' $dto='{}'", user, dto);
		}

		return service.limitReview(user.idx(), dto);
	}

	@Operation(summary = "회원탈퇴 요청")
	@PostMapping(URI.DELETE_ACCOUNT)
	public ResponseEntity<?> requestDeleteAccount(
		@Parameter(hidden = true) @CurrentUser CustomUser user,
		@RequestBody UserDto.DeleteUserAccount dto
		) {
		if (log.isInfoEnabled()) {
			log.info("([ requestDeleteAccount ]) $user='{}'", user);
		}
		service.sendEmailDeleteAccount(user.email(), dto.getPassword(), dto.getReason());
		// service.deleteUserByEmail(user.email());
		return ResponseEntity.ok().build();
	}

	// auth 서버 토큰으로 갈아칠 수 있도록 임시 조치
	@Operation(summary = "토큰 사용자 이메일 조회")
	@GetMapping("/tokens")
	public GowidResponseV2<String> getUserEmailViaToken(@RequestHeader(HttpHeaders.AUTHORIZATION) final String bearerToken) {
		final String accessToken = bearerToken.substring("Bearer ".length());
		return GowidResponseV2.ok(service.getUserEmailViaToken(accessToken));
	}

	@Deprecated		// FIXME BrochureController2에 유효성 체크 API 배포되면 삭제
	@Operation(summary = "도입 신청서 회원 검증")
	@GetMapping("/application/validate/{email}")
	public GowidResponseV2<Boolean> validateApplication(@PathVariable final String email) {
		return GowidResponseV2.ok(service.isPossibleCardApplication(email));
	}

	@Operation(summary = "회원 가입 링크 seed 만료 여부 검증")
	@PostMapping("/verify/registration/seed")
	public GowidResponseV2<SignUpSeedResDto> verifySeed(@Valid @RequestBody final SignUpSeedReqDto request) {
		return GowidResponseV2.ok(service.verifySeed(request.getSeed()));
	}
}
