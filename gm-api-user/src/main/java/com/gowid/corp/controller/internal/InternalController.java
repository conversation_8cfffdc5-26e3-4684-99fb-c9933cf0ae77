package com.gowid.corp.controller.internal;

import com.gowid.corp.config.ExpenseConfig;
import com.gowid.corp.controller.AbstractController;
import com.gowid.corp.core.annotation.CurrentUser;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.CorporateCompanyRes;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.ConsentResDto;
import com.gowid.corp.dto.UserDto;
import com.gowid.corp.dto.UserDto.TermsAndConsentReqDto;
import com.gowid.corp.dto.UserDto.TermsInfo;
import com.gowid.corp.dto.internal.*;
import com.gowid.corp.dto.internal.BulkUserResDto.BulkUserInfoDto;
import com.gowid.corp.dto.internal.InternalResDto.Auth;
import com.gowid.corp.dto.internal.InternalResDto.Corporation;
import com.gowid.corp.dto.internal.InternalResDto.User;
import com.gowid.corp.service.ConsentService;
import com.gowid.corp.service.UserService;
import com.gowid.corp.service.CorpService;
import com.gowid.corp.service.internal.InternalCorporationService;
import com.gowid.corp.service.internal.InternalUserService;
import com.gowid.corp.v2.dto.shinhan.D1530DTO;
import com.gowid.corp.v2.service.card.CorporateCompanyService;
import com.gowid.corp.v2.dto.EmailSeedVerifyDto;
import com.gowid.corp.v2.dto.UserActivationReqDto;
import com.gowid.corp.v2.service.card.register.RegStockholderService;
import com.gowid.corp.v2.service.scraping.FullTextService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController

@RequestMapping(InternalController.URI.BASE)
@RequiredArgsConstructor
@Tag(name = "Internal User / Corporation")
public class InternalController extends AbstractController {

    public static class URI {

        public static final String BASE = "/internal";
        public static final String CORPORATION = "/corporations";
        public static final String USER = "/users";
        public static final String USER_DIRECT = "/users/direct";
        public static final String MAIL = USER + "/mail";
        public static final String BULK = USER + "/bulk-excel";
        public static final String USER_ACTIVATION_MAIL = USER + "/activation-mail";
        public static final String STOCKHOLD = "/stockholders";

        public static final String SCRAPING_FULL_DATA_COLLECTION = "/scraping/full-data-collection";
        public static final String SCRAPING_COMPLETION = "/scraping/completion";

        public static final String CORPORATE_COMPANY = "/corporate-company";
    }

    private final ExpenseConfig expenseConfig;

    private final InternalCorporationService internalCorporationService;
    private final InternalUserService internalUserService;
    private final ConsentService consentService;
    private final RegStockholderService regStockholderService;
    private final CorporateCompanyService corporateCompanyService;
    private final FullTextService fullTextService;
    private final UserService userService;
    private final CorpService corpService;

    @Operation(summary = "법인 정보 조회(JWT)")
    @GetMapping(InternalController.URI.CORPORATION)
    public GowidResponseV2<Auth> getCorporationByToken(@Parameter(hidden = true) @CurrentUser CustomUser user) {
        return ok(internalCorporationService.getCorporationByUser(user.user().idx()));
    }

    @Operation(summary = "법인 정보 조회")
    @GetMapping(InternalController.URI.CORPORATION + "/{registrationNumber}")
    public GowidResponseV2<Corporation> getCorporation(@PathVariable String registrationNumber) {

        return ok(internalCorporationService.getCorporation(registrationNumber));
    }

    @Operation(summary = "고위드 데이터 시스템에서 2개년 스크래핑 완료시 호출되는 Callback")
    @PostMapping(InternalController.URI.SCRAPING_COMPLETION + "/{registrationNumber}")
    public GowidResponseV2<Boolean> callbackScrapingCompletion(@PathVariable String registrationNumber) {

        return ok(internalCorporationService.callbackScrapingCompletion(registrationNumber));
    }

    @Operation(summary = "고위드 데이터 시스템에 2개년 스크래핑 대사 처리 요청 (ondemand)")
    @PostMapping(InternalController.URI.SCRAPING_FULL_DATA_COLLECTION + "/{registrationNumber}")
    public GowidResponseV2<Boolean> scrapingFullDataCollection(@PathVariable String registrationNumber) {

        return ok(internalCorporationService.scrapingFullDataCollection(registrationNumber));
    }

    @Operation(summary = "유저 정보 조회")
    @GetMapping(InternalController.URI.USER + "/{email}")
    public GowidResponseV2<User> getUserByEmail(@PathVariable String email) {
        return ok(internalUserService.getUserByEmail(email));
    }

    @PreAuthorize("hasAnyRole('ROLE_MASTER','ROLE_EXPENSE_MANAGER','ROLE_CUSTOM')")
    @Operation(summary = "사용자 초대")
    @PostMapping(URI.USER)
    public GowidResponseV2<UserDto> addUser(@Parameter(hidden = true) @CurrentUser CustomUser user, @Valid @RequestBody InternalUserInviteReqDto dto,
        final HttpServletRequest request) {
        return ok(internalUserService.addUser(user.email(), dto, request.getHeader("Authorization")));
    }

    @Operation(summary = "사용자 초대(권한 체크 없음)")
    @PostMapping(URI.USER_DIRECT)
    public GowidResponseV2<UserDto> addUserDirect(@RequestBody InternalReqDto dto) {
        return ok(internalUserService.addUserDirect(dto));
    }

    @PreAuthorize("hasAnyRole('ROLE_MASTER','ROLE_EXPENSE_MANAGER','ROLE_CUSTOM')")
    @Operation(summary = "사용자 정보 수정")
    @PutMapping(URI.USER)
    public GowidResponseV2<BulkUserResDto> updateUser(
            @Parameter(hidden = true) @CurrentUser CustomUser user,
            @RequestHeader("Authorization") final String token,
    @RequestBody List<InternalUserUpdateReqDto> dtoList) {
        BulkUserResDto bulkUserResDto = BulkUserResDto.builder().totalCount(dtoList.size()).build();
        dtoList.forEach(dto -> {
            try {
                internalUserService.updateUser(user.user(), dto, token);
            } catch (Exception e) {
                log.error("[User] failed to update reason : {}", e.getMessage(), e);
                bulkUserResDto.getServerErrors().add(
                    BulkUserInfoDto.builder().email(dto.getEmail()).name(dto.getUserName()).errorMessage(e.getMessage()).build());
            }
        });

        return ok(bulkUserResDto);
    }

    @PreAuthorize("hasAnyRole('ROLE_MASTER','ROLE_EXPENSE_MANAGER','ROLE_CUSTOM')")
    @Operation(summary = "사용자 삭제")
    @DeleteMapping(URI.USER + "/{email}")
    public GowidResponseV2<Boolean> deleteUser(@Parameter(hidden = true) @CurrentUser CustomUser user, @PathVariable("email") String email,
        @RequestHeader("Authorization") final String token) {
        return ok(internalUserService.deleteUser(user.user(), email, token));
    }

    @PreAuthorize("hasAnyRole('ROLE_MASTER','ROLE_EXPENSE_MANAGER','ROLE_CUSTOM')")
    @Operation(summary = "멤버 초대메일 재발송")
    @PostMapping(URI.MAIL)
    public GowidResponseV2<Boolean> sendEmail(@Parameter(hidden = true) @CurrentUser CustomUser user,
        @Valid @RequestBody final InvitationReSendReqDto dto) {
        return ok(internalUserService.reSendInvitationMail(user.email(), dto));
    }

    @Operation(summary = "초대계정 활성화 인증 이메일 전송")
    @PostMapping(URI.USER_ACTIVATION_MAIL)
    public GowidResponseV2<String> sendUserActivationMail(@RequestBody UserActivationReqDto reqDto) {
        return ok(internalUserService.sendUserActivationMail(reqDto));
    }

    @Operation(summary = "활성화 인증 완료된 계정 인증 만료시간 초기화", description = "활성화 인증이 완료된 계정일 경우 패스워드 설정 시간을 위해 인증 만료시간을 5분으로 초기화합니다.")
    @PatchMapping(URI.USER_ACTIVATION_MAIL)
    public GowidResponseV2<Boolean> resetUserActivationVerifiedExpire(@Valid @RequestBody EmailSeedVerifyDto dto) {
        return ok(internalUserService.resetUserActivationVerifiedExpire(dto));
    }

    @PreAuthorize("hasAnyRole('ROLE_MASTER','ROLE_EXPENSE_MANAGER')")
    @Operation(summary = "사용자 다건 등록 파일 업로드")
    @PostMapping(URI.BULK)
    public GowidResponseV2<BulkUserResDto> addUsersViaExcel(@Parameter(hidden = true) @CurrentUser CustomUser user, @RequestPart MultipartFile file,
        final HttpServletRequest request) {
        return ok(internalUserService.addUsersViaExcel(user.idx(), user.corp(), file, request.getHeader("Authorization")));
    }

    @Operation(summary = "이용 약관 동의 여부를 확인", description = "이용 약관 동의 여부를 내려 줍니다.")
    @GetMapping("/terms-info")
    public GowidResponseV2<TermsInfo> getUserTermsInfo(@Parameter(hidden = true) @CurrentUser CustomUser user) {
        return GowidResponseV2.ok(internalUserService.getUserTermsInfo(user.idx()));
    }

    @Operation(summary = "이용 약관 및 개인정보, 마케팅 수신 여부를 저장", description = "이용 약관 및 개인정보, 마케팅 수신 여부를 저장합니다.")
    @PostMapping("/consent")
    public GowidResponseV2<ConsentResDto> saveReceptionConsent(@RequestHeader("apiKey") final String apiKey,
        @RequestBody TermsAndConsentReqDto termsAndConsentReqDto) {
        validApikey(apiKey);
        return GowidResponseV2.ok(consentService.saveReceptionConsent(termsAndConsentReqDto));
    }

    private void validApikey(String apiKey) {
        if (!expenseConfig.getApiKey().equals(apiKey)) {
            throw new GowidException(ResultCode.FORBIDDEN, "토큰 정보가 일치하지 않습니다.");
        }
    }


    @Operation(summary = "지출관리 사용자 초기화")
    @PostMapping(URI.USER + "/{userId}/expense")
    public GowidResponseV2<Boolean> initExpenseUser(@PathVariable Long userId, @RequestParam CardCompany cardCompany) {
        internalUserService.initExpenseUser(userId, cardCompany);
        return GowidResponseV2.ok(true);
    }

    @GetMapping(URI.STOCKHOLD + "/{id}")
    public GowidResponseV2<InternalResDto.StockholderFile> getStockholderFile(@PathVariable Long id) {
        return GowidResponseV2.ok(regStockholderService.getStockholderFileByIdOrThrow(id));
    }

    @GetMapping(URI.CORPORATE_COMPANY)
    public GowidResponseV2<List<CorporateCompanyRes>> getItems(@RequestParam List<String> corporateCompanyNumbers) {
        return GowidResponseV2.ok(corporateCompanyService.findByCorporateCompanyNumberIn(corporateCompanyNumbers));
    }

    @Operation(summary = "법인 등기부 등본 정보 수동 업데이트 (임시)")
    @PostMapping("/corp-registration/update")
    public GowidResponseV2<Boolean> updateCorporationRegistrationByD1530(@RequestParam Long userIdx,
                                                                         @RequestParam CardType cardType,
                                                                         @RequestParam CardCompany cardCompany,
                                                                         @RequestBody D1530DTO.D1530UpdateDTO reqDto) {
        return ok(fullTextService.manualUpdateCorpRegistrationByD1530Data(userIdx, reqDto, cardType, cardCompany));
    }
}
