package com.gowid.corp.controller;

import com.gowid.corp.core.dto.GowidResponse;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.ResponseObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;

import java.util.Collection;

@Slf4j
@Data
@Controller

public abstract class AbstractController {

    // TODO 응답메시지 바뀌면 제거
    @Deprecated
    protected GowidResponse okV1() {
        return okV1(null, null, null);
    }

    @Deprecated
    protected <T> GowidResponse<T> okV1(T data) {
        return okV1(data, null, null);
    }

    @Deprecated
    protected <T> GowidResponse<T> okV1(T data, Long totalCount) {
        return okV1(data, totalCount, null);
    }

    @Deprecated
    protected <T> GowidResponse<T> okV1(T data, String message) {
        return okV1(data, null, message);
    }

    @Deprecated
    protected <T> GowidResponse<T> okV1(T data, Long totalCount, String message) {
        GowidResponse<T> obj = new GowidResponse(data);
        obj.setTotalCount(totalCount);

        GowidResponse.ApiResult apiResult = GowidResponse.ApiResult.getSuccess();
        if (!StringUtils.isEmpty(message)) {
            apiResult.setExtraMessage(message);
        }
        obj.setResult(apiResult);
        if (data instanceof Collection && totalCount == null) {
            obj.setTotalCount((long) ((Collection<?>) data).size());
        }
        return obj;
    }

    protected <T> GowidResponseV2<T> ok() {
        return ok(null, null, null);
    }

    protected <T> GowidResponseV2<T> ok(T data) {
        return ok(data, null, null);
    }

    protected <T> GowidResponseV2<T> ok(T data, Long totalCount) {
        return ok(data, totalCount, null);
    }

    protected <T> GowidResponseV2<T> ok(T data, String message) {
        return ok(data, null, message);
    }

    protected <T> GowidResponseV2<T> ok(T data, Long totalCount, String message) {
        GowidResponseV2<T> obj = new GowidResponseV2(data);
        obj.setTotalCount(totalCount);

        ResponseObject responseObject = ResponseObject.getSuccess();
        if (!StringUtils.isEmpty(message)) {
            responseObject.setDesc(message);
        }
        obj.setResult(responseObject);
        if (data instanceof Collection && totalCount == null) {
            obj.setTotalCount((long) ((Collection<?>) data).size());
        }
        return obj;
    }

}
