package com.gowid.corp.controller.internal;

import com.gowid.corp.core.domain.cardIssuanceInfo.StockholderFileType;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.dto.internal.StockHolderUploadRequestDto;
import com.gowid.corp.service.internal.InternalStockHolderService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/internal/admin/stockholder")
public class StockHolderController {

    private final InternalStockHolderService internalStockHolderService;

    @Value("${admin.internal.api-key}")
    private String apiKey;

    @PostMapping
    public GowidResponseV2<Boolean> uploadFiles(
            @RequestHeader("Authorization") final String apiKey,
            @ModelAttribute final StockHolderUploadRequestDto request) {
        validHeader(apiKey);
        internalStockHolderService.uploadFile(request.getFileType(), request.getFiles(), request.getCardIssuanceInfoId());
        return GowidResponseV2.ok(true);
    }

    private void validHeader(final String header) {
        if (!apiKey.equals(header)) {
            throw new GowidException(ResultCode.FORBIDDEN, "토큰 정보가 일치하지 않습니다.");
        }
    }

    @DeleteMapping("/{cardIssuanceInfoId}")
    public GowidResponseV2<Boolean> deleteFiles(
            @RequestHeader("Authorization") final String apiKey, @PathVariable final Long cardIssuanceInfoId,
            @RequestParam final StockholderFileType fileType, @RequestParam final String fileName) {
        validHeader(apiKey);
        internalStockHolderService.deleteFiles(cardIssuanceInfoId, fileType, fileName);
        return GowidResponseV2.ok(true);
    }
}
