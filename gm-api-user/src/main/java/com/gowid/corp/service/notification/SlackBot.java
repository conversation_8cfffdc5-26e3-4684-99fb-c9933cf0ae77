package com.gowid.corp.service.notification;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SlackBot {
    BASIC_JUDGE("한도 조회", ":basic-judge:"),
    SPECIAL_JUDGE("특별 심사", ":special-judge:"),
    GRANT_SPECIAL_JUDGE("카드 발급(특심 부여)", ":grant-special-judge:"),
    NPS_SUBMIT("NPS 제출", ":gift_heart:"),
    FULL_TEXT_ERROR("전문 오류 알림 봇", ":소름:"),
    CARD_ISSUANCE_COMPLETE("카드 신청 완료 알림 봇", ":tada:"),
    CARD_SERVER("카드서버 봇", ":gowid:"),
    LIMIT_INCREASE_REQUESTED("한도상향 신청", ":here:"),
    BC_SIGNATURE_TEXT_ERROR("BC 전자 서명 오류 발생", ":no_entry_sign:"),
    BC_SIGNATURE_TEST_NULL("BC 전자 서명 누락 발생", ":bomb:"),
    BC_SUB_APPLY_ERROR("BC 추가 카드 발급 전문 오류", ":no_entry_sign:"),
    BC_SEND_FILE("BC 파일 전송", ":postbox:"),
    LIMIT_INCREASE_COMPLETED("한도상향 심사완료", ":white_check_mark:"),
    ;

    private final String name;
    private final String emoji;
}
