package com.gowid.corp.service.corporation;

import com.gowid.corp.type.KafKaTopic;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.kafka.annotation.KafkaListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
@RequiredArgsConstructor
public class NewCorpConsumeService {

//    private static final Logger logger = LoggerFactory.getLogger(NewCorpConsumeService.class);
//    @KafkaListener(topics = {KafKaTopic.BUSINESS_REGISTRATION_CORP_UPDATE}, groupId = "gm-api-user")
//    public void consumeBusinessRegistrationCorpUpdate(String message) {
//        logger.info("Received message from BUSINESS_REGISTRATION_CORP_UPDATE topic: {}", message);
//        // TODO: Add business logic to process the consumed message
//    }
}
