package com.gowid.corp.service;

import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.repository.user.UserRepository;
import com.gowid.corp.core.domain.user.Authority;
import com.gowid.corp.core.domain.user.Role;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.dto.AccountDto;
import com.gowid.corp.dto.AuthDto;
import com.gowid.corp.dto.EmailCheckResponseDto;
import com.gowid.corp.jwt.dto.TokenDto;
import com.gowid.corp.jwt.service.JwtService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final JwtService jwt;

    private final UserService userService;
    private final CorpService corpService;
    private final UserRepository repoUser;

    /**
     * 아이디(이메일) 존재여부 확인
     *
     * @param account 아이디(이메일)
     * @return EmailCheckResponseDto 아이디 존재 여부 response
     */
    public EmailCheckResponseDto isAlreadyExistUser(String account) {
        final Boolean isExistUser = repoUser.existsByAuthentication_EnabledAndEmail(true, account);

        if (!Boolean.TRUE.equals(isExistUser)) {
            return new EmailCheckResponseDto(false, "NOT_EXIST");
        }
        return new EmailCheckResponseDto(true);
    }

    /**
     * 사용자 계정 찾기
     *
     * @param name 이름
     * @param mdn  연락처(폰)
     * @return 계정 정보
     */
    @Transactional
    public List<String> findAccount(final String name, final String mdn) {
        if (StringUtils.isEmpty(name)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "이름은 필수 값입니다.");
        }

        if (StringUtils.isEmpty(mdn)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "전화번호는 필수 값입니다.");
        }

        return repoUser.findByNameAndMdnAndAuthentication_Enabled(name, mdn, true)
                .map(User::email)
                .map(email -> email.replaceAll("(^[^@]{3}|(?!^)\\G)[^@]", "$1*"))
                .collect(Collectors.toList());
    }

    /**
     * 인증토큰 갱신
     *
     * @param dto 인증토큰 갱신 DTO
     * @return 재발급된 인증토큰 정보 - 인증토큰, 생성일시, 만료일시
     */
    @Transactional
    public TokenDto.TokenSet reissueAccessToken(AccountDto dto) {
        User user = userService.findByEmailOrElseNull(dto.getEmail());
        if (ObjectUtils.isEmpty(user)) {
            throw new GowidException(ResultCode.NOT_EXIST_USER);
        }

        boolean isExistCorp = !ObjectUtils.isEmpty(user.corp());

        Role role = Authority.from(user.authorities());
        Set<Authority> authorities = new HashSet<>(user.authorities());
        TokenDto.TokenSet issuedToken = jwt.reissueAccessToken(dto.getEmail(), authorities, dto.getRefreshToken(), isExistCorp, role.name(), dto.getOsType());
        log.info("succeed to issue refresh token, userId : {}", user.idx());

        return issuedToken;
    }

    /**
     * 정보 조회
     * <p>
     * - 사용자 정보
     * - 소속 법인 정보
     * - 각 상태 정보
     *
     * @param idxUser 식별자(사용자)
     * @return 정보
     */
    @Transactional(readOnly = true)
    public AuthDto.AuthInfo info(final Long idxUser) {
        final User currentUser = userService.getByIdxOrThrow(idxUser);
        final Corp currentUserCorp = corpService.getCorpOrNull(currentUser);
        final User contractor = (currentUserCorp == null) ? null : userService.getOrThrow(currentUserCorp);
        final Long idxCorp = (currentUserCorp == null) ? null : currentUserCorp.idx();

        Set<Authority> authorities = new HashSet<>(currentUser.authorities());
        final boolean corpMapping = !ObjectUtils.isEmpty(currentUserCorp);
        final boolean isContractor = currentUser.isContractor(contractor);
        final boolean hasMasterRole = authorities.stream().anyMatch(i -> i.role().equals(Role.ROLE_MASTER)); // MASTER 여부
        Boolean isAgreeLimitIncreaseNoti = null;
        if (hasMasterRole && currentUserCorp != null) { // 법인의 한도상향 안내 동의여부는 MASTER 에게만 제공된다.
            isAgreeLimitIncreaseNoti = currentUserCorp.isAgreeLimitIncreaseNoti() != null ? currentUserCorp.isAgreeLimitIncreaseNoti() : false;
        }

        return AuthDto.AuthInfo.builder()
                .idx(currentUser.idx())
                .idxCorp(idxCorp)
                .isContractor(isContractor)
                .email(currentUser.email())
                .name(currentUser.name())
                .mdn(currentUser.mdn())
                .osType(currentUser.osType())
                .corpStatus(!ObjectUtils.isEmpty(currentUserCorp) ? currentUserCorp.status() : null)
                .corpName(currentUser.corpName())
                .corpIdentityNo(!ObjectUtils.isEmpty(currentUserCorp) ? currentUserCorp.resCompanyIdentityNo() : null)
                .info(TokenDto.TokenSet.AccountInfo.builder()
                        .authorities(authorities.stream().map(Authority::role).collect(Collectors.toList()))
                        .corpMapping(corpMapping)
                        .build()
                )
                .isSendSms(currentUser.reception().getIsSendSms())
                .isSendEmail(currentUser.reception().getIsSendEmail())
                .isAgreeLimitIncreaseNoti(isAgreeLimitIncreaseNoti)
                .build();
    }
}
