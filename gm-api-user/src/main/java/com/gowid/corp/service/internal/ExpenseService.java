package com.gowid.corp.service.internal;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.product.ProductActionLog;
import com.gowid.corp.core.domain.product.ProductActionType;
import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.corp.Product;
import com.gowid.corp.core.domain.corp.ServiceStatus;
import com.gowid.corp.core.domain.corp.ServiceType;
import com.gowid.corp.core.repository.corp.ProductRepository;
import com.gowid.corp.core.repository.stablemate.ProductActionLogRepository;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.utils.GowidUtils;
import com.gowid.corp.dto.feign.ExpenseCardCompanyResDto;
import com.gowid.corp.dto.feign.ExpenseCorpUpdateReqDto;
import com.gowid.corp.dto.feign.ExpenseInitReqDto;
import com.gowid.corp.dto.feign.ExpenseMemberReqDto;
import com.gowid.corp.dto.feign.ExpenseRoleResDto;
import com.gowid.corp.dto.feign.ExpenseUserInviteReqDto;
import com.gowid.corp.dto.feign.ExpenseUserUpdateReqDto;
import com.gowid.corp.feign.ExpenseFeignClient;
import com.gowid.corp.v2.service.authserver.AuthServerService;
import feign.FeignException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExpenseService {
    private final ExpenseFeignClient expenseFeignClient;

    private final ProductActionLogRepository productActionLogRepository;
    private final ProductRepository productRepository;

    private final AuthServerService authServerService;

    @Value("${feign.service.expense.api-key}")
    private String apiKey;

    @Transactional(noRollbackFor = Exception.class)
    public void initInfo(Long userIdx, Corp corp, CardCompany cardCompany) {
        if (ObjectUtils.isEmpty(corp.resCompanyIdentityNo())) {
            log.error("[유저 초기화] 요청한 유저의 사업자등록번호가 없습니다. $idxUser = {}, idxCorp = {}, resCompanyIdentityNo = {}", userIdx, corp.idx(), corp.resCompanyIdentityNo());
            throw new GowidException(ResultCode.NOT_FOUND, "법인 정보가 없습니다.");
        }

        ResponseEntity<GowidResponseV2<Object>> response;
        ProductActionLog productActionLog = ProductActionLog.builder()
                .type(ProductActionType.EXPENSE_USER_INIT)
                .value(corp.resCompanyIdentityNo())
                .user(User.builder().idx(userIdx).build())
                .build();

        boolean isSuccess;
        try {
            response = expenseFeignClient.initInfo(apiKey, new ExpenseInitReqDto(corp.resCompanyIdentityNo(), cardCompany));
            isSuccess = setStablemate(response, productActionLog);
        } catch (Exception e) {
            log.error("[Expense init feign client] Failed to call API");
            isSuccess = false;
            productActionLog.message(e.getMessage());
        }

        if (isSuccess) {
            Product dbProduct = productRepository.findByCorpAndServiceType(corp, ServiceType.EXPENSE);
            if (ObjectUtils.isEmpty(dbProduct)) {
                Product product = Product.builder()
                        .corp(corp)
                        .serviceType(ServiceType.EXPENSE)
                        .serviceStatus(ServiceStatus.READY)
                        .build();

                productRepository.save(product);
                authServerService.addProduct(corp.resCompanyIdentityNo(), ServiceType.EXPENSE, ServiceStatus.READY);
            }
        } else {
            // 외부 서비스 호출 에러 저장(에러 추적 및 관리자 또는 postman에서 재시도 API 호출 시 사용하기 위함)
            productActionLogRepository.save(productActionLog);
        }
    }

    public void addUserNew(ExpenseUserInviteReqDto dto, final String bearerToken) {
        try {
            ResponseEntity<GowidResponseV2<Object>> response = expenseFeignClient.addUserNew(bearerToken, dto);
            if (ObjectUtils.isEmpty(response) || !response.hasBody()) {
                throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 추가 시 에러가 발생하였습니다.");
            }
        } catch (FeignException e) {
            log.error("지출관리 사용자 추가 시 에러가 발생하였습니다. Error msg: {}", e.getMessage(), e);
            //TODO 삭제 시 추가하도록 구현
//            hardDeleteUser(dto);
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 추가 시 에러가 발생하였습니다.");
        }
    }

    public void updateUserNew(final ExpenseUserUpdateReqDto dto, final String bearerToken) {
        try {
            ResponseEntity<GowidResponseV2<Object>> response = expenseFeignClient.updateUserNew(bearerToken, dto);
            if (ObjectUtils.isEmpty(response) || !response.hasBody()) {
                throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 수정 시 에러가 발생하였습니다.");
            }
        } catch (FeignException e) {
            log.error("지출관리 사용자 수정 시 에러가 발생하였습니다. Error msg: {}", e.getMessage(), e);
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 수정 시 에러가 발생하였습니다.");
        }
    }

    public void updateUser(final ExpenseUserUpdateReqDto dto) {
        try {
            ResponseEntity<GowidResponseV2<Object>> response = expenseFeignClient.updateUser(apiKey, dto);
            if (ObjectUtils.isEmpty(response) || !response.hasBody()) {
                throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 수정 시 에러가 발생하였습니다.");
            }
        } catch (FeignException e) {
            log.error("지출관리 사용자 수정 시 에러가 발생하였습니다. Error msg: {}", e.getMessage(), e);
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 수정 시 에러가 발생하였습니다.");
        }
    }

    public void deleteUser(ExpenseMemberReqDto dto, String bearerToken) {
        try {
            ResponseEntity<GowidResponseV2<Object>> response = expenseFeignClient.softDeleteUser(bearerToken, dto);
            if (ObjectUtils.isEmpty(response) || !response.hasBody()) {
                throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 삭제 시 에러가 발생하였습니다.");
            }
        } catch (FeignException e) {
            log.error("지출관리 사용자 삭제 시 에러가 발생하였습니다. Error msg: {}", e.getMessage(), e);
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 삭제 시 에러가 발생하였습니다.");
        }
    }

    public void hardDeleteUser(ExpenseMemberReqDto dto) {
        try {
            ResponseEntity<GowidResponseV2<Object>> response = expenseFeignClient.hardDeleteUser(apiKey, dto);
            if (ObjectUtils.isEmpty(response) || !response.hasBody()) {
                throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 hard 삭제 시 에러가 발생하였습니다.");
            }
        } catch (FeignException e) {
            log.error("지출관리 사용자 삭제 시 에러가 발생하였습니다. Error msg: {}", e.getMessage(), e);
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 사용자 hard 삭제 시 에러가 발생하였습니다.");
        }
    }

    private boolean setStablemate(ResponseEntity<GowidResponseV2<Object>> response, ProductActionLog productActionLog) {
        boolean result = true;
        String responseMsg = hasResponse(response);
        if (StringUtils.isEmpty(responseMsg)) {
            if (!ObjectUtils.isEmpty(response.getBody()) && ResultCode.SUCCESS.getCode() != response.getBody().getResult().getCode()) {
                result = false;
                productActionLog.code(String.valueOf(response.getBody().getResult().getCode()));
                productActionLog.message(response.getBody().getResult().getDesc());
            }
        } else {
            result = false;
            productActionLog.message(responseMsg);
        }
        return result;
    }

    private String hasResponse(ResponseEntity<?> response) {
        if (ObjectUtils.isEmpty(response)) {
            return "응답이 존재하지 않습니다.";
        } else if (!response.hasBody()) {
            return "응답 본문 값이 존재하지 않습니다.";
        }
        return null;
    }

    @Async("gowidExecutor")
    public void updateCorporation(final JSONObject response) {
        try {
            expenseFeignClient.updateCorporation(apiKey,
                    GowidUtils.getEmptyStringToString(response, "resCompanyIdentityNo"),
                    new ExpenseCorpUpdateReqDto(
                            GowidUtils.getEmptyStringToString(response, "resCompanyNm"),
                            GowidUtils.getEmptyStringToString(response, "resUserAddr"),
                            GowidUtils.getEmptyStringToString(response, "resOpenDate"),
                            GowidUtils.getEmptyStringToString(response, "resRegisterDate"),
                            GowidUtils.getEmptyStringToString(response, "resBusinessTypes"),
                            GowidUtils.getEmptyStringToString(response, "resBusinessItems")
                    ));
        } catch (Exception e) {
            log.error("지출 법인 데이터 갱신 실패. 사업자 등록번호 : {}, 사유 : {}",
                    GowidUtils.getEmptyStringToString(response, "resCompanyIdentityNo"),
                    e.getMessage());
        }

    }

    public ExpenseCardCompanyResDto getCorpCardCompanyInfo(String registrationNumber) {
        GowidResponseV2<ExpenseCardCompanyResDto> response = null;
        try {
            response = expenseFeignClient.getCorpCardCompanyInfo(apiKey, registrationNumber);
        } catch (Exception e) {
            log.error("지출 법인 카드사 정보 조회 실패. 사업자 등록번호 : {}, 사유 : {}", registrationNumber, e.getMessage(), e);
        }

        if (response == null || response.getData() == null) {
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 카드사 정보 조회에 실패하였습니다.");
        }

        return response.getData();
    }

    public List<ExpenseRoleResDto> getCorpRoles(String bearerToken) {
        GowidResponseV2<List<ExpenseRoleResDto>> response = null;
        try {
            response = expenseFeignClient.getCorpRoles(bearerToken);
        } catch (Exception e) {
            log.error("지출관리 권한 정보 조회 실패, 사유 : {}", e.getMessage(), e);
        }

        if (response == null || response.getData() == null) {
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 권한 정보 조회가 실패하였습니다.");
        }

        return response.getData();
    }

    public boolean isEligibleUser(String bearerToken) {
        GowidResponseV2<Boolean> response = null;
        try {
            response = expenseFeignClient.isEligibleUser(bearerToken);
        } catch (Exception e) {
            log.error("지출관리 FUEL 권한 여부 조회 실패, 사유 : {}", e.getMessage(), e);
        }

        if (response == null || response.getData() == null) {
            throw new GowidException(ResultCode.EXPENSE_EXTERNAL_EXCEPTION, "지출관리 FUEL 권한 여부 조회에 실패하였습니다.");
        }

        return response.getData();
    }
}
