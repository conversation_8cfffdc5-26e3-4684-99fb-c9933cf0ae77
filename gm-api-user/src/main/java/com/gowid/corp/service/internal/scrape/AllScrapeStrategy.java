package com.gowid.corp.service.internal.scrape;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.type.ScrapeType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AllScrapeStrategy implements ScrapeStrategy {

    private final BusinessRegisterStrategy businessRegisterStrategy;
    private final FinancialStatementStrategy financialStatementStrategy;
    private final CorporateRegisterStrategy corporateRegisterStrategy;

    @Override
    public void scrape(final Long corpId, final Long cardIssuanceInfoId) {
        try {
            businessRegisterStrategy.scrape(corpId, cardIssuanceInfoId);
        } catch (Exception e) {
            throw new GowidException(ResultCode.BAD_REQUEST, "사업자 등록증 스크래핑에 실패했습니다.");
        }

        try {
            financialStatementStrategy.scrape(corpId, cardIssuanceInfoId);
        } catch (Exception e) {
            throw new GowidException(ResultCode.BAD_REQUEST, "재무제표 스크래핑에 실패했습니다.");
        }

        try {
            corporateRegisterStrategy.scrape(corpId, cardIssuanceInfoId);
        } catch (Exception e) {
            throw new GowidException(ResultCode.BAD_REQUEST, "등기부등본 스크래핑에 실패했습니다.");
        }
    }

    @Override
    public ScrapeType getScrapeType() {
        return ScrapeType.ALL;
    }
}
