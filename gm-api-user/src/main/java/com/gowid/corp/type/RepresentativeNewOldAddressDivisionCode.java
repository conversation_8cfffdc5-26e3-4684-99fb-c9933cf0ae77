package com.gowid.corp.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RepresentativeNewOldAddressDivisionCode {
    OLD_POSTAL_OLD_ADDRESS("1", "구우편번호 구주소"), // 구우편번호 구주소
    OLD_POSTAL_NEW_ADDRESS("2", "구우편번호 새주소"), // 구우편번호 새주소
    NEW_POSTAL_OLD_ADDRESS("3", "새우편번호 구주소"), // 새우편번호 구주소
    NEW_POSTAL_NEW_ADDRESS("4", "새우편번호 새주소") // 새우편번호 새주소
    ;

    private final String code;
    private final String desc;

}
