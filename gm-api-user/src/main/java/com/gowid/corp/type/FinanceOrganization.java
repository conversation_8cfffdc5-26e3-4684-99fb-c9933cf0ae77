package com.gowid.corp.type;

import com.gowid.corp.core.domain.common.CommonCodeType;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FinanceOrganization {

    KDB_BANK("0002", CommonCodeType.BANK_1, "산업은행"),
    IBK_BANK("0003", CommonCodeType.BANK_1, "기업은행"),
    KB_BANK("0004", CommonCodeType.BANK_1, "국민은행"),
    SUHYUP_BANK("0007", CommonCodeType.BANK_1, "수협은행"),
    NH_BANK("0011", CommonCodeType.BANK_1, "농협은행"),
    WOORI_BANK("0020", CommonCodeType.BANK_1, "우리은행"),
    SC_BANK("0023", CommonCodeType.BANK_1, "SC은행"),
    CITY_BANK("0027", CommonCodeType.BANK_1, "씨티은행"),
    DEAGU_BANK("0031", CommonCodeType.BANK_1, "대구은행"),
    BUSAN_BANK("0032", CommonCodeType.BANK_1, "부산은행"),
    KWANGJU_BANK("0034", CommonCodeType.BANK_1, "광주은행"),
    JEJU_BANK("0035", CommonCodeType.BANK_1, "제주은행"),
    JEONBUK_BANK("0037", CommonCodeType.BANK_1, "전북은행"),
    KYONGNAM_BANK("0039", CommonCodeType.BANK_1, "경남은행"),
    KFCC_BANK("0045", CommonCodeType.BANK_1, "새마을금고"),
    CREDIT_UNION_BANK("0048", CommonCodeType.BANK_1, "신협은행"),
    EPOST_BANK("0071", CommonCodeType.BANK_1, "우체국"),
    KEB_BANK("0081", CommonCodeType.BANK_1, "KEB하나은행"),
    SHINHAN_BANK("0088", CommonCodeType.BANK_1, "신한은행"),
    K_BANK("0089", CommonCodeType.BANK_1, "K뱅크"),

    SHINHAN_STOCK("0278", CommonCodeType.STOCK, "신한금융투자"),
    KOREA_INVESTMENT_STOCK("0243", CommonCodeType.STOCK, "한국투자증권"),
    MIRAEASSET_STOCK("0238", CommonCodeType.STOCK, "미래에셋대우"),
    KYOBO_STOCK("0261", CommonCodeType.STOCK, "교보증권"),
    SK_STOCK("0266", CommonCodeType.STOCK, "SK증권"),
    HANWHA_STOCK("0269", CommonCodeType.STOCK, "한화증권"),
    MERITZ_STOCK("0287", CommonCodeType.STOCK, "메리츠증권"),
    EUGENE_STOCK("0280", CommonCodeType.STOCK, "유진투자증권"),
    SAMSUNG_STOCK("0240", CommonCodeType.STOCK, "삼성증권"),
    KB_STOCK("0218", CommonCodeType.STOCK, "KB증권"),
    YUANTA_STOCK("0209", CommonCodeType.STOCK, "유안타증권"),
    NH_STOCK("0247", CommonCodeType.STOCK, "NH투자증권"),
    HI_STOCK("0262", CommonCodeType.STOCK, "하이투자증권"),
    KIWOOM_STOCK("0264", CommonCodeType.STOCK, "키움증권"),
    EBEST_STOCK("0265", CommonCodeType.STOCK, "이베스트투자증권"),
    DAISHIN_STOCK("0267", CommonCodeType.STOCK, "대신증권"),
    HANA_STOCK("0270", CommonCodeType.STOCK, "하나금융투자"),
    DB_STOCK("0279", CommonCodeType.STOCK, "동부증권"),
    NAMU_STOCK("1247", CommonCodeType.STOCK, "모바일증권 나무"),
    CREON_STOCK("1267", CommonCodeType.STOCK, "크레온"),
    IBK_STOCK("0225", CommonCodeType.STOCK, "IBK투자증권"),
    DAOL_STOCK("0227", CommonCodeType.STOCK, "다올투자증권"),
    ;

    private final String organizationCode;
    private final CommonCodeType organizationType;
    private final String organizationName;

    public static FinanceOrganization findFinanceOrganizationOrNull(final String organizationCode) {
        if (!StringUtils.hasText(organizationCode)) {
            throw new GowidException(ResultCode.BAD_REQUEST);
        }

        return Arrays.stream(FinanceOrganization.values())
                .filter(s -> s.organizationCode.equals(organizationCode))
                .findAny()
                .orElse(null);
    }

}
