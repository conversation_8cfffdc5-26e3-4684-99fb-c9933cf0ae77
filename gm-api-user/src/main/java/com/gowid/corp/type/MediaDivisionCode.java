package com.gowid.corp.type;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MediaDivisionCode {
    MS("1", "MS"), // MS
    IC("2", "IC"), // IC
    MS_IC("3", "MS+IC"), // MS+IC
    VSDC("4", "VSDC"), // VSDC
    MS_MODEX("5", "MS+MODEX"), // MS+MODEX
    MS_RF("6", "MS+RF"), // MS+RF
    MD_POSTPAY("7", "MD+후불"), // MD+후불
    MD_PREPAY("8", "MD+선불"), // MD+선불
    MS_RF_ID("9", "MS+RF(신분증)") // MS+RF(신분증)
    ;

    private final String code;
    private final String name;
}
