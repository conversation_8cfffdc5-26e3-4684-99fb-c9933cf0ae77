package com.gowid.corp.config;

import com.gowid.corp.utils.SalesforceMessage;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;

@Configuration
public class SalesforceConfig {

    @Value("${salesforce.enabled}")
    private Boolean enabled;

    @Bean
    public InitializingBean salesforceMessageInitializer(final KafkaTemplate<String, Object> kafkaTemplate) {
        return () -> {
            SalesforceMessage.setSalesforceEnabled(enabled);
            SalesforceMessage.setPublisher(kafkaTemplate);
        };
    }
}
