package com.gowid.corp.config;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.service.FuelService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Aspect
@Component
@RequiredArgsConstructor
public class FuelEligibilityAspect {

    private final FuelService fuelService;

    @Around("@annotation(com.gowid.corp.core.annotation.FuelEligible)")
    public Object checkEligibility(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new IllegalStateException("RequestAttributes not found (no current HTTP request context)");
        }

        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader("Authorization");

        if (!fuelService.isEligibleUser(token)) {
            throw new GowidException(ResultCode.UNAUTHORIZED_INVALID_TOKEN, "FUEL 대상 법인의 최초 계약자만 확인할 수 있습니다.");
        }

        return joinPoint.proceed();
    }
}
