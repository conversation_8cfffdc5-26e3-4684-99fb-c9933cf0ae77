package com.gowid.corp.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.gowid.corp.controller.AuthController;
import com.gowid.corp.core.utils.Const;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Enumeration;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class LoggingAspect<T> {

    private final HttpServletRequest request;
    private final Gson gson;
    private final ObjectMapper objectMapper;

    @Before("execution(* com.gowid.corp..controller..*(..)) " +
            "&& !execution(* com.gowid.corp.controller.error..*(..)) " +
            "&& !execution(* com.gowid.corp.controller.base..*(..))")
    public void beforeController(JoinPoint joinPoint) {
        final String bodies = getRequestBodies(joinPoint);

        log.debug("\n");
        log.debug("########################## Request START ##########################\n" +
                        "## API: {} {}\n" +
                        "## Header: {}\n" +
                        "## Request body: {}" +
                        "\n########################## Request END ##########################\n",
                request.getMethod(), request.getRequestURI(),
                getHeaders(request),
                StringUtils.hasText(bodies) ? bodies : "No request body");
    }

    @AfterReturning(pointcut = "execution(* com.gowid.corp..controller..*(..))", returning = "result")
    public void afterController(Object result) {
        String bodies = "";
        try {
            if (Const.IGNORE_LOG_RES.contains(request.getRequestURI())) {
                bodies = "이용약관(BrandConsent.class) 응답 생략";
            } else {
                bodies = getResponseBody(result);
            }
        } catch (Exception e) {
            log.error("Error occurs while parsing response={}", e.getMessage(), e);
            // do nothing
        } finally {
            log.debug("\n");
            log.debug("########################## Response START ##########################\n" +
                            "## API: {} {}\n" +
                            "## Response body: {}" +
                            "\n########################## Response End ##########################\n",
                    request.getMethod(), request.getRequestURI(),
                    StringUtils.hasText(bodies) ? bodies : "No Response body");
        }
    }

    private String getResponseBody(final Object result) {
        if (result instanceof ResponseEntity) {
            return getWithInnerClassBody((ResponseEntity<T>) result);
        } else {
            return gson.toJson(result);
        }
    }

    // legacy 중 inner class 사용이 많음. gson inner class 역직렬화 불가 문제.
    private String getWithInnerClassBody(ResponseEntity<T> result) {
        final T responseEntityBody = getResponseEntityBody(result);

        try {
            return objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(responseEntityBody);
        } catch (JsonProcessingException e) {
            log.error("{} occurs while parsing response={}",
                    e.getClass().getSimpleName(), e.getMessage(), e);
            // do nothing
        }

        return result.toString();
    }

    private T getResponseEntityBody(final ResponseEntity<T> result) {
        return result.getBody();
    }

    private String getRequestBodies(final JoinPoint joinPoint) {
        if (hasAuthInfoBody(joinPoint)) {
            return "Skip logging auth info";
        }

        final Method method = getMethod(joinPoint);
        final Parameter[] parameters = method.getParameters();
        final Annotation[][] annotations = method.getParameterAnnotations();
        final Object[] args = joinPoint.getArgs();

        StringBuilder stringBuilder = new StringBuilder();
        for (int argIndex = 0; argIndex < args.length; argIndex++) {
            for (Annotation paramAnnotation : annotations[argIndex]) {
                if ((paramAnnotation instanceof RequestBody) || (paramAnnotation instanceof ModelAttribute)) {
                    Parameter parameter = parameters[argIndex];
                    Object arg = args[argIndex];
                    stringBuilder.append(parameter.getName()).append(gson.toJson(arg));
                }
            }
        }

        return stringBuilder.toString();
    }

    private boolean hasAuthInfoBody(final JoinPoint joinPoint) {
        return joinPoint.getSignature().getDeclaringType().equals(AuthController.class) ||
                joinPoint.getSignature().getDeclaringType().equals(com.gowid.corp.v2.controller.AuthController.class);
    }

    private String getHeaders(final HttpServletRequest request) {
        final Enumeration<String> headerNames = request.getHeaderNames();
        final StringBuilder stringBuilder = new StringBuilder();

        while (headerNames.hasMoreElements()) {
            final String headerName = headerNames.nextElement();
            final String headerValue = request.getHeader(headerName);
            stringBuilder.append("#").append(headerName).append(": ").append(headerValue).append(", ");
        }

        return gson.toJson(stringBuilder);
    }

    private static Method getMethod(final JoinPoint joinPoint) {
        final Signature signature = joinPoint.getSignature();

        return ((MethodSignature) signature).getMethod();
    }

}
