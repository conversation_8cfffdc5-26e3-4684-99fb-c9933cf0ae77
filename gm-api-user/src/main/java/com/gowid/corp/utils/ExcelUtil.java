package com.gowid.corp.utils;

import com.gowid.corp.core.utils.Const;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFCell;

@UtilityClass
@Slf4j
public class ExcelUtil {

    public static String getCellStringValue(XSSFCell cell) {
        if (cell == null) {
            return Const.EMPTY_STRING;
        }
        return cell.getStringCellValue().replaceAll("\b", Const.EMPTY_STRING);
    }

    public static boolean getCellBooleanValue(XSSFCell cell) {
        if (cell == null) {
            return false;
        }
        return cell.getBooleanCellValue();
    }
}
