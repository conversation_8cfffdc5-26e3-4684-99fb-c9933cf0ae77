package com.gowid.corp.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class DashboardInsightMessageService {

    private final KafkaTemplate<String, Object> kafkaTemplate;


    public void publish(final String topic, Object event) {
        if (kafkaTemplate != null) {
            try {
                kafkaTemplate.send(topic, event);
            } catch (Exception e) {
                log.error("[DASHBOARD INSIGHT] 메시지 발송에 실패했습니다 - {}", topic, e);
            }
        }
    }
}
