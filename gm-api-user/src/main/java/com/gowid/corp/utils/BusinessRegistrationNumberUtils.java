package com.gowid.corp.utils;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;

import java.util.Arrays;
import java.util.List;

public  class BusinessRegistrationNumberUtils {
    static final List<String> PROFIT_CODE = Arrays.asList("81", "85", "86", "87", "88");

    public static Boolean isProfitCompany(String businessRegistrationNumber) {
        String[] codes = businessRegistrationNumber.split("-");
        if(codes.length != 3) {
            throw new GowidException(ResultCode.CORP_REGISTRATION_NUMBER_EXCEPTION);
        }

        return PROFIT_CODE.contains(codes[1]);
    }
}
