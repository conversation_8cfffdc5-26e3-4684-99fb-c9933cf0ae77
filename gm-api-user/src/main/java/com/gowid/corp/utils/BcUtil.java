package com.gowid.corp.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Slf4j
@UtilityClass
public class BcUtil {


    public static String makeTransactionKey(String aes256SecretKey){
        String uuid = UUID.randomUUID().toString();
        String key = AESUtil.encryptAES256(uuid, aes256SecretKey);
        String transactionKey = key.substring(0, 9);
        return transactionKey;
    }

    public static String makeBcAmountForm(Long amount){
        Long bcAmount = amount / 10000;
        String str = String.format("%015d", bcAmount);
        return str;
    }
    public static String makeFileNum(Integer num){
        String str = String.format("%02d", num);
        return str;
    }

    public static String makeBcAmountFormBy1010(Long amount){
        String str = String.valueOf(amount);
        return str;
    }

    public static String makeBcAmountToString(String amount){
        if(amount == null || amount.equals("")){
            return "0";
        }
        Long amountL = Long.parseLong(amount);
        Long bcAmount = amountL / 10000;
        String str = String.valueOf(bcAmount);
        return str;
    }

    public static String makeFileName(){
        // TODO: 16시 ~ 23시 사이에 실행 될 경우 오늘 날짜의 08시로 기록 되는 오류 존재. 수정 필요.
        String Time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH"));
        String FileName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int timeNum = Integer.parseInt(Time);
        String str = "";
        if(timeNum < 8 ){
            str = "08";
        } else if( timeNum > 8 && timeNum < 12){
            str = "12";
        } else if(timeNum > 12 && timeNum < 16){
            str = "16";
        } else {
            str ="ERROR";
            return str;
        }
        return FileName + str;
    }
}
