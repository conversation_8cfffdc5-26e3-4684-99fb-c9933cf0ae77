package com.gowid.corp.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;

@Slf4j
public class SalesforceMessage {


    private static Boolean SALESFORCE_ENABLED;

    private static KafkaTemplate<String, Object> kafkaTemplate;

    public static void setPublisher(final KafkaTemplate<String, Object> publisher) {
        SalesforceMessage.kafkaTemplate = publisher;
    }

    public static void setSalesforceEnabled(Boolean value) {
        SALESFORCE_ENABLED = value;
    }


    public static void publish(final String topic, Object event) {
        if (kafkaTemplate != null && SALESFORCE_ENABLED) {
            try {
                kafkaTemplate.send(topic, event);
            } catch (Exception e) {
                log.error("{} 메시지 발송에 실패했습니다. 사유 : ", topic, e);
            }
        }
    }
}
