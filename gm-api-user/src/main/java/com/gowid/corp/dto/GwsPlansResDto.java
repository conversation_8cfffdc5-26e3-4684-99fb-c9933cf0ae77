package com.gowid.corp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Schema(description = "플랜 정보 DTO")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@JsonInclude(Include.NON_NULL)
public class GwsPlansResDto {

    @Schema(description = "플랜 리스트")
    private List<GwsPlan> plans;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Getter
    @Setter
    public static class GwsPlan {

        private String key;

        private String title;

        private String description;

        private BigDecimal primeCost;

        private BigDecimal discountedPrice;

        private BigDecimal gowidDiscountedPrice;

        private BigDecimal defaultDiscountRate;
    }
}
