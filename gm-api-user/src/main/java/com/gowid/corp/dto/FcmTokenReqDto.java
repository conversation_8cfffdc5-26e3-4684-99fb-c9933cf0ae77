package com.gowid.corp.dto;

import com.gowid.corp.core.domain.user.OsType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class FcmTokenReqDto {

    private Long userId;
    @Schema(description = "OS 타입(IOS, ANDROID)")
    @NotEmpty(message = "os type is not null")
    private OsType osType;
    @Schema(description = "FCM 토큰 정보")
    @NotEmpty(message = "token type is not null")
    private String token;
    @Schema(description = "기기 모델명")
    private String deviceModel;
    @Schema(description = "기기 버전")
    private String osVersion;
    @Schema(description = "App 버전(app/web version)")
    private String appVersion;

}
