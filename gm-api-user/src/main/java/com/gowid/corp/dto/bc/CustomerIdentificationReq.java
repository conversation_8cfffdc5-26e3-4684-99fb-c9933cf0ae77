package com.gowid.corp.dto.bc;

import lombok.*;

@Setter
@Getter
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerIdentificationReq {

    private String receptionSerialNumber;
    private String receptionDateTime;

    @Builder.Default
    private String transactionType = "00";
    @Builder.Default
    private String customerType = "04";
    @Builder.Default
    private String relationshipWithSelf = "임직원";
    private CustomerIdentity customerIdentity;
    private IdentityVerification identityVerification;
    private CreationDate creationDate;
    private MobileVerification mobileVerification;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerIdentity {
        private String nationality;
        private Domestic domestic;
        private Foreigner foreigner;
        private Common common;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Domestic {
            private String koreanName;
            private String englishName;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Foreigner {
            private String koreanName;
            private String englishName;
            private String dateOfBirth;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Common {
            private String realNameNumber;
            @Builder.Default
            private String addressType = "01";
            private String address;
            private String mobilePhoneNumber;
            private String email;
            @Builder.Default
            private String occupation = "00";
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IdentityVerification {
        private String realNameCertificate;
        @Builder.Default
        private String verificationMethod = "00";
        private DrivingLicense drivingLicense;
        private String issueDate;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DrivingLicense {
            private String drivingLicenseNumber;
            private String issueDate;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreationDate {
        private String year;
        private String month;
        private String day;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MobileVerification {
        private String key;
        private String verfiedDate;
    }
}
