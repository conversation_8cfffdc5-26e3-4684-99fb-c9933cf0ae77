package com.gowid.corp.dto.feign;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AuthUserConsentReqDto {

    private String email;
    private Boolean isSendSms;
    private LocalDateTime smsConsentedAt;
    private Boolean isSendEmail;
    private LocalDateTime emailConsentedAt;
    private List<AuthConsentReqDto> consents;

}
