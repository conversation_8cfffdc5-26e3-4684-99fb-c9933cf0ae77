package com.gowid.corp.dto.corporation;

import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Getter
public class RepresentativeReqDto {
    private Long representativeId;
    private String name;
    private String engName;
    private String birth;
    private String gender;
    private String identificationType;
    private String identificationIssuedDate;
    private String identityNumber;
    private String nation;
    private String phoneNumber;
    private String agency;
    private String drivingLicenseNumber;
}
