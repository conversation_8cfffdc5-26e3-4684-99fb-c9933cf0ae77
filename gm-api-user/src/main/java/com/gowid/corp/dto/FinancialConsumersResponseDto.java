package com.gowid.corp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gowid.corp.core.domain.bc.FinancialConsumerProtectionActBc;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.lotte.FinancialConsumerProtectionActLotte;
import com.gowid.corp.core.domain.shinhan.FinancialConsumerProtectionActShinhan;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@JsonInclude
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinancialConsumersResponseDto {

    private Long cardIssuanceInfoIdx;

    private Boolean overFiveEmployees;

    private CardIssuanceDto.FinancialConsumerProtectionActResByShinhan financialConsumerProtectionActShinhan;

    private CardIssuanceDto.FinancialConsumerProtectionActResByLotte financialConsumerProtectionActLotte;

    private CardIssuanceDto.FinancialConsumerProtectionActResByBc financialConsumerProtectionActBc;


    public static FinancialConsumersResponseDto from(CardIssuanceInfo cardIssuanceInfo,
                                                     @Nullable final FinancialConsumerProtectionActShinhan finConProtectActShinhan,
                                                     @Nullable final FinancialConsumerProtectionActLotte finConProtectActLotte,
                                                     @Nullable final FinancialConsumerProtectionActBc finConProtectActBc) {
        return FinancialConsumersResponseDto
                .builder()
                .cardIssuanceInfoIdx(cardIssuanceInfo.idx())
                .overFiveEmployees(cardIssuanceInfo.getFinancialConsumers().getOverFiveEmployees())
                .financialConsumerProtectionActShinhan(CardIssuanceDto.FinancialConsumerProtectionActResByShinhan.from(finConProtectActShinhan))
                .financialConsumerProtectionActLotte(CardIssuanceDto.FinancialConsumerProtectionActResByLotte.from(finConProtectActLotte))
                .financialConsumerProtectionActBc(CardIssuanceDto.FinancialConsumerProtectionActResByBc.from(finConProtectActBc))
                .build();
    }

}
