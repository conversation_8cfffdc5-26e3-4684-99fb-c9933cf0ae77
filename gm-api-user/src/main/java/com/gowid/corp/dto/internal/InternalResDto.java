package com.gowid.corp.dto.internal;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceStatus;
import com.gowid.corp.core.domain.cardIssuanceInfo.StockholderFileType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

public class InternalResDto {

  @Builder
  @Getter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class User {

    @Schema(description = "GOWID 사용자 ID")
    private Long gowidUserId;

    @Schema(description = "이메일")
    private String email;

    @Schema(description = "전화번호")
    private String phoneNumber;

    @Schema(description = "유저 이름")
    private String name;

    @Schema(description = "직책")
    private String position;

    @Schema(description = "사업자 등록 번호")
    private String corporationRegistrationNumber;
  }

  @Builder
  @Getter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Corporation {

    @Schema(description = "GOWID 법인 ID")
    private Long gowidCorporationId;

    @Schema(description = "사업자 유형")
    private String ownershipType;

    @Schema(description = "사업장 업태")
    private String businessCategory;

    @Schema(description = "사업장 업종")
    private String businessItem;

    @Schema(description = "주소")
    private String address;

    @Schema(description = "법인 영문명")
    private String englishName;

    @Schema(description = "법인명")
    private String name;

    @Schema(description = "우편번호")
    private String zipCode;

    @Schema(description = "사업자 등록 번호")
    private String corporationRegistrationNumber;

    @Schema(description = "개업일자")
    private String businessStartDate;

    @Schema(description = "사업등록일자")
    private String businessRegistrationDate;

    @Schema(description = "개인/법인 등록 번호")
    private String identifyingNumber;

    @Schema(description = "직원수")
    private Integer numberOfEmployees;

    @Schema(description = "직원수 업데이트 일자")
    private String employeeCountUpdateDate;

    @Schema(description = "대표자 정보")
    private User representative;

    @Schema(description = "카드 발급 정보")
    private List<Card> cards;
  }

  @Builder
  @Getter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Card {

    @Schema(description = "카드 타입")
    private CardType type;

    @Schema(description = "카드 회사")
    private CardCompany corporation;

    @Schema(description = "발급 상태")
    private IssuanceStatus status;
  }

  @Builder
  @Getter
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Auth {

    @Schema(description = "사업자 등록 번호")
    private String businessRegistrationNumber;

    @Schema(description = "법인명")
    private String corporationName;

    @Schema(description = "이메일")
    private String email;
  }

  @Builder
  @Getter
  public static class StockholderFile {
    private Long id;
    private StockholderFileType fileType;
    private String gcsPath;
    private String registrationNumber;
  }
}
