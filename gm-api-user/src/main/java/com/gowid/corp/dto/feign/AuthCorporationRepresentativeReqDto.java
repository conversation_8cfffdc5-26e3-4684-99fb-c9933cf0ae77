package com.gowid.corp.dto.feign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AuthCorporationRepresentativeReqDto {

    private String requesterEmail;
    private String name;
    private String birth;
    private String nationality;
    private String gender;
    private String position;
    private String contactNumber;

}
