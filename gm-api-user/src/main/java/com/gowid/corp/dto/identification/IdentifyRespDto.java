package com.gowid.corp.dto.identification;

import com.gowid.corp.v2.dto.baas.IdentificationRes;
import com.gowid.corp.v2.service.identification.IdentifyType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@Getter
@Setter
public class IdentifyRespDto {
    private Long targetIdx;
    private IdentifyType identifyType;
    private String code;
    private String message;

    public void setResponse(IdentificationRes resp){
        this.code = resp.getCode();
        this.message = resp.getMessage();
    }

    public void setSaveResponse(IdentifyType identifyType, Long targetIdx){
        this.targetIdx = targetIdx;
        this.identifyType = identifyType;
    }
}
