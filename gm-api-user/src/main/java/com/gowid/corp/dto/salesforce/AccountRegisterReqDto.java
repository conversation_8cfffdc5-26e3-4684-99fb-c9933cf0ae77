package com.gowid.corp.dto.salesforce;

import lombok.Value;

@Value
public class AccountRegisterReqDto {
    Long cardIssuanceInfoId;
    String email;
    AccountInsertType accountInsertType;

    private AccountRegisterReqDto(final Long cardIssuanceInfoId, final String email, final AccountInsertType accountInsertType) {
        this.cardIssuanceInfoId = cardIssuanceInfoId;
        this.email = email;
        this.accountInsertType = accountInsertType;
    }

    public static AccountRegisterReqDto register(final String email) {
        return new AccountRegisterReqDto(null, email, AccountInsertType.REGISTRATION);
    }

    public static AccountRegisterReqDto cardIssuance(final Long cardIssuanceInfoId, final String email) {
        return new AccountRegisterReqDto(cardIssuanceInfoId, email, AccountInsertType.CARD_ISSUANCE);
    }

    public static AccountRegisterReqDto cardIssuance(final Long cardIssuanceInfoId) {
        return new AccountRegisterReqDto(cardIssuanceInfoId, null, AccountInsertType.CARD_ISSUANCE);
    }

    public static AccountRegisterReqDto apply(final Long cardIssuanceInfoId) {
        return new AccountRegisterReqDto(cardIssuanceInfoId, null, AccountInsertType.APPLY);
    }

    public static AccountRegisterReqDto issued(final Long cardIssuanceInfoId) {
        return new AccountRegisterReqDto(cardIssuanceInfoId, null, AccountInsertType.ISSUED);
    }

    private enum AccountInsertType {
        CARD_ISSUANCE,
        APPLY,
        ISSUED,
        REGISTRATION
    }
}
