package com.gowid.corp.dto.feign;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.gowid.corp.core.domain.user.UserStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseUserUpdateReqDto {

    @Schema(description = "사업자 등록번호")
    private String companyIdentityNo;

    @Schema(description = "고위드 사용자 ID")
    private Long gowidUserId;

    @Schema(description = "이메일")
    private String email;

    @Schema(description = "사용자 이름")
    private String name;

    @Schema(description = "직책")
    private String position;

    private Long departmentId;

    @Schema(description = "연락처")
    private String phone;

    @Schema(description = "권한")
    private String roleType;

    private Long roleId;

    @Schema(description = "상태")
    private UserStatus status;

    @Schema(description = "구독 담당자 여부")
    @JsonProperty(defaultValue = "false")
    private Boolean isOfficesuite;

}
