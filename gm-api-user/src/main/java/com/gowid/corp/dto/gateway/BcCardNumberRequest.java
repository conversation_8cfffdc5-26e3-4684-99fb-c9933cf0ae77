package com.gowid.corp.dto.gateway;

import com.gowid.corp.type.CardBrand;
import com.gowid.corp.type.CardGrade;
import com.gowid.corp.type.CardHolderType;
import com.gowid.corp.type.CardVipType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BcCardNumberRequest {
    private CardBrand brand;
    private CardGrade cardClass;
    private CardHolderType applicationCardType;
    private String businessCardCode;
    private CardVipType platinumClass;
    private String voucherCode;
    private String cardProductNumber;
    private String individualBusiness;


}
