package com.gowid.corp.dto;

import com.gowid.corp.type.VerifyCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailReqDto {

    @Email(message = "잘못된 이메일 형식입니다.")
    private String email;

    private VerifyCode type;

}
