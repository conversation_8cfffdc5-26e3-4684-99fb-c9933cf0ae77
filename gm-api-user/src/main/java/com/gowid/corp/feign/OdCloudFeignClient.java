package com.gowid.corp.feign;

import com.gowid.corp.dto.user.OdCloudResponseDto;
import com.gowid.corp.dto.user.RegistrationNumberDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "odcloud", url = "${data-portal-api.url}")
public interface OdCloudFeignClient {
    @PostMapping("/api/nts-businessman/v1/status")
    ResponseEntity<OdCloudResponseDto> getNtsBusinessCheck(@RequestBody RegistrationNumberDto dto, @RequestParam("serviceKey") String apiKey);

}
