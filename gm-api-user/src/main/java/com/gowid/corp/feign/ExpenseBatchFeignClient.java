package com.gowid.corp.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name="expense-batch", url="${feign.service.expense-batch.url}")
public interface ExpenseBatchFeignClient {
    @PostMapping(value = "/on-demands/card-limit/bc/{encType}")
    void onDemandBcFlf1330(@PathVariable("encType") String encType, @RequestHeader("Authorization") String Authorization, @RequestBody List<String> cardNumbers);
}
