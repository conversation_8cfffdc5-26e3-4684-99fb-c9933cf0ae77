package com.gowid.corp.feign;

import com.gowid.corp.core.dto.AccessManagementAnnounceDto;
import com.gowid.corp.core.dto.GowidResponse;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.v2.dto.certificate.CertificateConnectedInfoResDto;
import com.gowid.corp.v2.service.certificate.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "certificate", url = "${feign.service.certificate.url}", configuration = FeignConfig.class)
public interface CertificateFeignClient {

    @PostMapping(value = "/certificates", consumes = MediaType.APPLICATION_JSON_VALUE)
    GowidResponse<CertificateResDto> registerCertificate(@RequestHeader("Authorization") String apiKey, @RequestBody CertificateReqDto reqDto);

    @GetMapping(value = "/certificates")
    GowidResponseV2<List<CertificateDto>> getCertificates(@RequestHeader("Authorization") String apiKey, @RequestParam("registrationNumber") String registrationNumber);

    @GetMapping(value = "/certificates/{id}/connected-organizations")
    GowidResponseV2<CertificateConnectedInfoResDto> getConnectedOrganizations(@RequestHeader("Authorization") String apiKey,
                                                                              @PathVariable("id") Long id,
                                                                              @RequestParam("organizationSearchStatus") ConnectedOrganizationSearchDto.OrganizationSearchStatus organizationSearchStatus);
    @GetMapping(value = "/certificates/announcements")
    GowidResponseV2<AccessManagementAnnounceDto> getCertificateAnnouncements(@RequestHeader("Authorization") String apiKey,
                                                                             @RequestParam("registrationNumber") String registrationNumber);

    @DeleteMapping(value = "/certificates/{id}")
    GowidResponseV2<Boolean> deleteCertificate(@RequestHeader("Authorization") String apiKey, @PathVariable("id") Long id, @RequestParam("registrationNumber") String registrationNumber);
}
