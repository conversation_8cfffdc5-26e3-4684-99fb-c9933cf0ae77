package com.gowid.corp.exception;

import com.gowid.corp.core.dto.response.ErrorCode;
import com.gowid.corp.core.exception.BaseException;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class BusinessException extends BaseException {

	public BusinessException(String code, String message) {
		this.code = code;
		this.desc = message;
	}

	public BusinessException(ErrorCode.External externalError) {
		this.code = externalError.getCode();
		this.desc = externalError.getDesc();
		this.extraMessage = externalError.getDesc();
	}

	public BusinessException(ErrorCode.External externalError, String extraMessage) {
		this.code = externalError.getCode();
		this.desc = externalError.getDesc();
		this.extraMessage = extraMessage;
	}

	public BusinessException(ErrorCode.External externalError, String extraCode, String extraMessage) {
		this.code = externalError.getCode();
		this.desc = externalError.getDesc();
		this.extraCode = extraCode;
		this.extraMessage = extraMessage;
	}

}
