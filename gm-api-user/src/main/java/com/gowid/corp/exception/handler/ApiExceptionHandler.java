package com.gowid.corp.exception.handler;

import com.gowid.corp.core.exception.CardDocumentException;
import com.gowid.corp.dto.gateway.ApiResponse;
import com.gowid.corp.exception.AlreadyExistException;
import com.gowid.corp.exception.BadRequestException;
import com.gowid.corp.exception.CorpAlreadyExistException;
import com.gowid.corp.exception.ExpiredException;
import com.gowid.corp.exception.NotRegisteredException;
import com.gowid.corp.exception.ShinhanFulltextException;
import com.gowid.corp.exception.ShinhanInternalException;
import com.gowid.corp.exception.SurveyAlreadyExistException;
import com.gowid.corp.exception.SystemException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RequiredArgsConstructor
@RestControllerAdvice
public class ApiExceptionHandler {

    @ExceptionHandler(NotRegisteredException.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    protected ApiResponse<?> handleBadRequestException(NotRegisteredException e) {
        return ApiResponse.builder()
            .result(ApiResponse.ApiResult.builder()
                .code(e.getCode())
                .desc(e.getDesc())
                .build())
            .build();
    }

    @ExceptionHandler({BadRequestException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    protected ApiResponse<?> handleBadRequestException(BadRequestException e) {
        return ApiResponse.builder()
                .result(ApiResponse.ApiResult.builder()
                        .code(e.getCode())
                        .desc(e.getDesc())
                        .build())
                .build();
    }

    @ExceptionHandler({ShinhanFulltextException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    protected ApiResponse<?> handleShinhanFulltextException(ShinhanFulltextException e) {
        return ApiResponse.builder()
            .result(ApiResponse.ApiResult.builder()
                .code(e.getResponse().getResponseCode())
                .desc(e.getResponse().getResponseMessage())
                .extraMessage(e.getExtraMessage())
                .build())
            .build();
    }

    @ExceptionHandler({CorpAlreadyExistException.class, SurveyAlreadyExistException.class})
    @ResponseStatus(HttpStatus.CONFLICT)
    protected ApiResponse<?> handleCorpAlreadyExistException(AlreadyExistException e) {
        return ApiResponse.builder()
                .result(ApiResponse.ApiResult.builder()
                        .code(e.category())
                        .desc(e.resource())
                        .build())
                .build();
    }

    @ExceptionHandler({ExpiredException.class})
    @ResponseStatus(HttpStatus.GONE)
    protected ApiResponse<?> handleExpiredException(ExpiredException e) {
        return ApiResponse.builder()
                .result(ApiResponse.ApiResult.builder()
                        .code(e.getErrorCodeDescriptor().category())
                        .desc(e.getErrorCodeDescriptor().error())
                        .build())
                .build();
    }

    @ExceptionHandler(SystemException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    protected ApiResponse<?> handleSystemException(SystemException e) {
        return ApiResponse.builder()
            .result(ApiResponse.ApiResult.builder()
                .code(e.getCode())
                .desc(e.getDesc())
                .build())
            .build();
    }

    @ExceptionHandler({ShinhanInternalException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    protected ApiResponse<?> handleShinhanInternalException(ShinhanInternalException e) {
        return ApiResponse.builder()
            .result(ApiResponse.ApiResult.builder()
                .code(e.getCode())
                .desc(e.getDesc())
                .extraMessage(e.getShinhanMessage())
                .build())
            .build();
    }

    @ExceptionHandler(CardDocumentException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    protected ApiResponse<?> handleCardDocumentException(CardDocumentException e) {
        return ApiResponse.builder()
            .result(ApiResponse.ApiResult.builder()
                .code(e.getCode())
                .desc(e.getDesc())
                .extraMessage(e.getExtraMessage())
                .build())
            .build();
    }

}
