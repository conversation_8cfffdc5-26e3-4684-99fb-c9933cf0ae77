package com.gowid.corp.exception;

import com.gowid.corp.core.dto.response.ErrorCode;
import com.gowid.corp.core.exception.BaseException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import com.gowid.corp.core.exception.result.ResultType;
import lombok.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BadRequestException extends BaseException {
	private String code;
	private String desc;

	public BadRequestException(ErrorCode.Api errorCodeType) {
		code = errorCodeType.getCode();
		desc = errorCodeType.getDesc();
	}

	public BadRequestException(ErrorCode.Api errorCodeType, String addString) {
		code = errorCodeType.getCode();
		desc = errorCodeType.getDesc() + " - " + addString;
	}

	public BadRequestException(GowidMessageGroup gowidMessageGroup) {
		super(ResultType.INVALID_DATA, gowidMessageGroup);
	}

}
