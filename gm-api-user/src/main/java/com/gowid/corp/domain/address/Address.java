package com.gowid.corp.domain.address;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Arrays;

@Getter
public class Address {
    protected static final int START_IDX = 0;
    protected static final String DELIMITER = ",";
    protected static final String PARENTHESIS = "(";

    private String address;
    private String addressDetail;

    public Address(String userAddr) {
        validate(userAddr);
        String[] addresses = splitAddress(userAddr);

        this.address = addresses[START_IDX];
        this.addressDetail = addresses.length > 1 ? addresses[1].trim() : null;
    }

    public void setAddressDetailByCorpName(String corpName){
        this.addressDetail = corpName;
    }

    private void validate(String userAddr) {
        if (!StringUtils.hasText(userAddr)) {
            throw new GowidException(ResultCode.USER_ADDRESS_IS_REQUIRED);
        }
    }


    private String[] splitAddress(String userAddr) {
        int indexOfParenthesis = userAddr.indexOf(PARENTHESIS);
        int indexOfComma = userAddr.indexOf(DELIMITER);
        String[] splitAddress;
        String[] result = null;

        // "("이 먼저 등장하거나 ","가 없는 경우
        if (indexOfParenthesis != -1 && (indexOfComma == -1 || indexOfParenthesis < indexOfComma)) {
            splitAddress = userAddr.split("\\(");

            if (splitAddress.length > 1) {
                result = new String[] {splitAddress[0], String.join("(", Arrays.copyOfRange(splitAddress, 1, splitAddress.length))};
            }

            // 문자열이 ")"로 끝나는지 확인
            if (result[1] != null && result[1].endsWith(")")) {
                // 마지막 문자 ")" 제거
                result[1] = result[1].substring(0, result[1].length() - 1);
            }

        } else if (indexOfComma != -1) {
            splitAddress = userAddr.split(DELIMITER);

            if (splitAddress.length > 1) {
                result = new String[] {splitAddress[0], String.join(DELIMITER, Arrays.copyOfRange(splitAddress, 1, splitAddress.length))};
            }
        } else {
            result = new String[] {userAddr};
        }

        return result;
    }
}
