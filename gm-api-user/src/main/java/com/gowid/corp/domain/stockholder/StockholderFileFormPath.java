package com.gowid.corp.domain.stockholder;

import com.gowid.corp.core.domain.cardIssuanceInfo.StockholderFileType;

import java.util.HashMap;
import java.util.Map;

import static com.gowid.corp.domain.stockholder.StockholderFilePath.*;

public class StockholderFileFormPath {
    private static final String FORM_MID_PATH = "/forms";

    private static final String STOCKHOLDER_FORM_DOCX = "/(고위드)롯데카드 주주명부 양식.docx";
    private static final String STOCKHOLDER_FORM_XLSX = "/(고위드)롯데카드 주주명부 양식.xlsx";
    private static final String STOCKHOLDER_FORM_HWP = "/(고위드)롯데카드 주주명부 양식.hwp";

    private static final String ACTUAL_OWNER_FORM_PDF = "/(고위드)롯데카드 실제 소유자 확인서 양식.pdf";

    private static final Map<String, String> paths = new HashMap<>();

    static {
        paths.put("stockholder-docx", STOCKHOLDER + FORM_MID_PATH + STOCKHOLDER_FORM_DOCX);
        paths.put("stockholder-xlsx", STOCKHOLDER + FORM_MID_PATH + STOCKHOLDER_FORM_XLSX);
        paths.put("stockholder-hwp", STOCKHOLDER + FORM_MID_PATH + STOCKHOLDER_FORM_HWP);
        paths.put("actual_owner-pdf", ACTUAL_OWNER + FORM_MID_PATH + ACTUAL_OWNER_FORM_PDF);
    }

    public static String convertToStringPath(String fileType, String fileExtension) {
        return paths.get(getPathType(fileType) + KEY_DELIMITER + fileExtension.toLowerCase());
    }

    private static String getPathType(String fileType) {
        if (StockholderFileType.from(fileType).isActualOwner()) {
            return ACTUAL_OWNER;
        }

        return STOCKHOLDER;
    }

    public static String getFileName(String filePath) {
        return filePath.substring(filePath.lastIndexOf(PATH_DELIMITER) + 1);
    }
}
