package com.gowid.corp.domain.stockholder;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.StockholderFileType;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.utils.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

import static java.util.stream.Collectors.toList;

@Slf4j
public class StockholderGatewayPathMaker {
    private final Map<String, Integer> sequenceMap;

    public StockholderGatewayPathMaker(MultipartFile[] files) {
        List<MultipartFile> sortedFiles = sortedByFileName(files);
        this.sequenceMap = makeSequenceMapOfFiles(sortedFiles);
    }

    private List<MultipartFile> sortedByFileName(MultipartFile[] files) {
        return Arrays.stream(files)
                .sorted(Comparator.comparing(MultipartFile::getOriginalFilename).reversed())
                .collect(toList());
    }

    // 사용자가 등록한 원본 파일(pdf)과 FE에서 변환된 파일(jpg, jpeg)이 같은 파일명을 갖도록 하는 로직.
    private Map<String, Integer> makeSequenceMapOfFiles(List<MultipartFile> sortedFiles) {
        Map<String, Integer> sequenceMap = new TreeMap<>();
        int sequence = 1;

        for (MultipartFile file : sortedFiles) {
            if (!FilenameUtils.getExtension(file.getOriginalFilename()).equalsIgnoreCase("pdf")) {
                sequenceMap.put(file.getOriginalFilename(), sequence++);
                continue;
            }

            sequenceMap.put(file.getOriginalFilename(), sequence);
        }

        return sequenceMap;
    }

    public String makeSequence(MultipartFile file, StockholderFileType fileType) {
        return fileType.getCode() + "00" + sequenceMap.get(file.getOriginalFilename());
    }

    public String makeFileName(String orgFileName, String licenseNo, String sequence, String gwFileCode) {
        String fileExtension = FilenameUtils.getExtension(orgFileName);

        if (fileExtension.equals("pdf")) {
            return licenseNo + gwFileCode + sequence + "_back." + FilenameUtils.getExtension(orgFileName);
        }

        return licenseNo + gwFileCode + sequence + "." + FilenameUtils.getExtension(orgFileName);
    }

    public static String getGwFileCode(CardCompany cardCompany, StockholderFileType fileType) {
        switch (cardCompany) {
            case SHINHAN:
                return Const.SHINHAN_STOCKHOLDER_GW_FILE_CODE;
            case LOTTE:
                if (fileType.isActualOwner()) {
                    return Const.LOTTE_ACTUAL_OWNER_GW_FILE_CODE;
                }
                return Const.LOTTE_STOCKHOLDER_GW_FILE_CODE;
            case BC:
                if (fileType.isActualOwner()) {
                    return Const.BC_ACTUAL_OWNER_GW_FILE_CODE;
                }
                return Const.BC_STOCKHOLDER_GW_FILE_CODE;
            default:
                throw new GowidException(ResultCode.EXCEPTION, "유효한 카드사가 아닙니다.");
        }
    }
}
