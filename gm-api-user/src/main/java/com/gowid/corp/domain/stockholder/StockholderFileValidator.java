package com.gowid.corp.domain.stockholder;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
public class StockholderFileValidator {
    private static final int LIMITED_GOWID_SUB_FILE_COUNT = 8;

    public static void isEmpty(List<MultipartFile[]> allFiles) {
        if (allFiles.stream().allMatch(ObjectUtils::isEmpty)) {
            throw new GowidException(ResultCode.INVALID_PARAMETER, "모든 주주명부 파일이 비어있을 수 없습니다.");
        }
    }

    public static void validateFiles(MultipartFile[] files) {
        if (files.length > LIMITED_GOWID_SUB_FILE_COUNT) {
            log.error("[STOCKHOLDER] 파일 최대 갯수({})를 초과되었습니다.", LIMITED_GOWID_SUB_FILE_COUNT);
            throw new GowidException(ResultCode.INVALID_PARAMETER, "주주명부 파일 개수가 등록 가능한 개수를 초과했습니다.");
        }
    }
}
