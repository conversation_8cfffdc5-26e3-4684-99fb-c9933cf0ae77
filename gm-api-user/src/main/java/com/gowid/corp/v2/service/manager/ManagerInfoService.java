package com.gowid.corp.v2.service.manager;

import com.gowid.corp.core.domain.cardIssuanceInfo.ManagerInfo;
import com.gowid.corp.core.repository.querydsl.CustomManagerInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ManagerInfoService {

    private final CustomManagerInfoRepository customManagerInfoRepository;

    public ManagerInfo getByIdxCardIssuanceInfoOrNull(final Long idxCardIssuanceInfo) {
        return customManagerInfoRepository.findByIdxCardIssuanceInfo(idxCardIssuanceInfo);
    }

}
