package com.gowid.corp.v2.dto.scraping;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CorporationStockAccountsResDto {

    private String registrationNumber;            // 사업자등록번호
    private String organizationCode;              // 기관코드
    private String account;                       // 계좌번호
    private String accountWithHyphen;             // 하이픈 추가된 계좌번호
    @Nullable private String accountType;                   // 계좌 유형
    @Nullable private String accountNickname;               // 계좌 별칭
    @Nullable private BigDecimal principalAmount;           // 원금
    @Nullable private BigDecimal purchaseAmount;            // 매입금액
    @Nullable private BigDecimal valuationAmount;           // 평가금액
    @Nullable private BigDecimal valuationProfitLoss;       // 평가손익
    @Nullable private BigDecimal loanAmount;                // 대출금액
    @Nullable private BigDecimal withdrawableBalance;       // 출금가능금액
    @Nullable private BigDecimal depositBalance;            // 예수금
    @Nullable private BigDecimal depositBalanceD1;          // 예수금 D+1
    @Nullable private BigDecimal depositBalanceD2;          // 예수금 D+2
    @Nullable private BigDecimal depositBalanceForeign;     // 외화예수금
    @Nullable private BigDecimal earningsRatio;             // 수익률 %
}
