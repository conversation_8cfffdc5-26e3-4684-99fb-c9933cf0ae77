package com.gowid.corp.v2.service.auth;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.exception.MismatchedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class AuthValidator {

	private final PasswordEncoder encoder;

	public boolean matchedPassword(String oldPassword, String newPassword){
		boolean matched = encoder.matches(oldPassword, newPassword);
		if (!matched) {
			throw MismatchedException.builder().category(MismatchedException.Category.PASSWORD).build();
		}
		return true;
	}

	public void expiredVerifyCodeThrowException(String storedVerifyCode){
		if (StringUtils.isEmpty(storedVerifyCode)) {
			throw new GowidException(ResultCode.VERIFICATION_CODE_EXPIRED_EXCEPTION);
		}
	}

	public void mismatchedVerifyCodeThrowException(String targetCode, String storedVerifyCode){
		if (!targetCode.equals(storedVerifyCode)) {
			throw new GowidException(ResultCode.VERIFICATION_CODE_MISMATCH_EXCEPTION);
		}
	}

	public String encodePassword(String password){
		return encoder.encode(password);
	}

}
