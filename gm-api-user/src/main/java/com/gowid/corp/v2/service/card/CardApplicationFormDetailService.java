package com.gowid.corp.v2.service.card;

import com.gowid.corp.core.domain.bc.BillingUnitLimit;
import com.gowid.corp.core.domain.bc.CardApplicationForm;
import com.gowid.corp.core.domain.bc.CardApplicationFormDetail;
import com.gowid.corp.core.domain.bc.CorporateCompany;
import com.gowid.corp.core.domain.bc.type.*;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CeoInfo;
import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.dto.*;
import com.gowid.corp.core.dto.response.*;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.exception.NotFoundException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import com.gowid.corp.core.repository.bc.BillingUnitLimitRepository;
import com.gowid.corp.core.repository.bc.CardApplicationFormDetailRepository;
import com.gowid.corp.core.repository.bc.CardApplicationFormRepository;
import com.gowid.corp.core.repository.bc.CorporateCompanyRepository;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.corp.CeoInfoRepository;
import com.gowid.corp.core.repository.querydsl.CustomCardApplicationFormDetailRepository;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.gateway.BcCorporationRequest;
import com.gowid.corp.dto.gateway.BcCorporationResponse;
import com.gowid.corp.exception.BadRequestException;
import com.gowid.corp.service.bc.BcFullTextService;
import com.gowid.corp.type.BcIssuanceType;
import com.gowid.corp.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.*;

import java.time.LocalDateTime;

import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class CardApplicationFormDetailService {

    private final CustomCardApplicationFormDetailRepository customCardApplicationFormDetailRepository;
    private final CardApplicationFormDetailRepository cardApplicationFormDetailRepository;
    private final CardApplicationFormRepository cardApplicationFormRepository;
    private final CorporateCompanyRepository corporateCompanyRepository;
    private final CardIssuanceInfoRepository cardIssuanceInfoRepository;
    private final CeoInfoRepository ceoInfoRepository;

    private final BillingUnitLimitService billingUnitLimitService;
    private final BillingUnitLimitRepository billingUnitLimitRepository;

    private final BcFullTextService bcFullTextService;

    public Page<CardApplicationFromDetailRes> searchCardApplicationFormDetail(CafSearchKeywordCriteriaDto request, Pageable pageable, CustomUser user) {
        Page<CardApplicationFromDetailRes> cardApplicationFromDetailRes =
                customCardApplicationFormDetailRepository.searchCardApplicationFormDetail(request, pageable, user.corp());

        List<CardApplicationFromDetailRes> content = cardApplicationFromDetailRes.getContent();
        content.forEach(item -> {
            item.setIsDeferredTrafficCardDivisionCode(item.getDeferredTrafficCardDivisionCode().equals("null") ? false : true);
        });

        return cardApplicationFromDetailRes;
    }

    public GetCafdCountStatusRes getCountByStatusInCaf(CustomUser user) {
        Map<IssuanceStatusType, Long> countStatusMap = customCardApplicationFormDetailRepository.getCountByStatusInCaf(user.corp());
        long createdCount = countStatusMap.get(IssuanceStatusType.CREATED) == null ? 0L : countStatusMap.get(IssuanceStatusType.CREATED);
        long pendingCount = countStatusMap.get(IssuanceStatusType.PENDING) == null ? 0L : countStatusMap.get(IssuanceStatusType.PENDING);
        long rejectCount = countStatusMap.get(IssuanceStatusType.REJECT) == null ? 0L : countStatusMap.get(IssuanceStatusType.REJECT);
        long readyCount = countStatusMap.get(IssuanceStatusType.READY) == null ? 0L : countStatusMap.get(IssuanceStatusType.READY);
        long sentCount = countStatusMap.get(IssuanceStatusType.SENT) == null ? 0L : countStatusMap.get(IssuanceStatusType.SENT);
        long issuedCount = countStatusMap.get(IssuanceStatusType.ISSUED) == null ? 0L : countStatusMap.get(IssuanceStatusType.ISSUED);

        return GetCafdCountStatusRes.builder()
                .pendingApprovalCount(createdCount)
                .pendingIssuanceCount(readyCount + sentCount + pendingCount + rejectCount)
                .issuedCount(issuedCount)
                .build();
    }

    @Transactional
    public CafdApplyValidateResDto applyValidate(CardApplicationFormDetailApplyReqDto request, CustomUser user) {
        Corp corp = user.corp();
        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoRepository.findByCorpAndCardCompany(corp, CardCompany.BC)
                .orElseThrow(() -> new NotFoundException(GowidMessageGroup.G00005));

        List<CeoInfo> ceoInfo = ceoInfoRepository.getByCardIssuanceInfoOrderByCeoNumberAsc(cardIssuanceInfo);

        if (ceoInfo.isEmpty()) {
            throw new GowidException(ResultCode.BC_CII_CEO_INFO);
        }

        // CAF에 ceoInfo 세팅
        request.getCardApplicationFormDetailApplyList().forEach(item -> {
            CardApplicationFormDetail cardApplicationFormDetail = cardApplicationFormDetailRepository.findById(item.getIdx()).orElseThrow(() -> new NotFoundException(GowidMessageGroup.G00005));
            cardApplicationFormDetail.cardApplicationForm().ceoInfo(ceoInfo.get(0));
        });

        List<BillingUnitLimitRes> billingUnitLimits = billingUnitLimitService.getBillingUnitLimit(user.corp());

        BillingUnitLimitRes publicBillingUnit = new BillingUnitLimitRes();
        BillingUnitLimitRes privateBillingUnit = new BillingUnitLimitRes();

        log.info("billingUnitLimits - size: {}", billingUnitLimits.size());

        for (BillingUnitLimitRes billingUnitLimit : billingUnitLimits) {
            log.info(" - billingUnitType : ", billingUnitLimit.getBillingUnitType());

            if (billingUnitLimit.getBillingUnitType().equals(ApplicationType.PUBLIC)) {
                publicBillingUnit = billingUnitLimit;
            } else {
                privateBillingUnit = billingUnitLimit;
            }
        }

        List<CardApplicationFormDetailApplyDto> corporateDtoList =
                request.getCardApplicationFormDetailApplyList().stream()
                        .filter(item -> item.getAccountType().equals(AccountType.CORPORATION))
                        .collect(Collectors.toList());

        List<CardApplicationFormDetailApplyDto> personalDtoList =
                request.getCardApplicationFormDetailApplyList().stream()
                        .filter(item -> item.getAccountType().equals(AccountType.PERSONAL))
                        .collect(Collectors.toList());

        return calcValidation(corporateDtoList, personalDtoList, publicBillingUnit, privateBillingUnit);
    }

    @Transactional
    public Boolean cafdApply(CardApplicationFormDetailApplyReqDto request, CustomUser user) {
        CafdApplyValidateResDto validateResDto = applyValidate(request, user);
        if (!validateResDto.getIsValidate()) {
            throw new GowidException(ResultCode.EXCEPTION, "신청 가능한 한도를 초과하였습니다.");
        }

        LocalDateTime now = LocalDateTime.now();
        request.getCardApplicationFormDetailApplyList().forEach(item -> {
            CardApplicationFormDetail cardApplicationFormDetail = cardApplicationFormDetailRepository.findById(item.getIdx())
                    .orElseThrow(() -> new NotFoundException(GowidMessageGroup.G00005));

            CardApplicationForm cardApplicationForm = cardApplicationFormDetail.cardApplicationForm();

            String resCompanyIdentityNo = cardApplicationForm.corp().resCompanyIdentityNo();

            // 개인 계좌일 경우
            if (item.getAccountType().equals(AccountType.PERSONAL)) {

                // 업체번호 채번
                BcCorporationResponse bcCorporationResponse = getBcCorporationCompanyNumber(resCompanyIdentityNo);

                CorporateCompany corporateCompany = corporateCompanyRepository.save(
                        CorporateCompany.builder()
                                .corp(user.corp())
                                .accountType(item.getAccountType())
                                .state(CorporateCompanyType.CREATED)
                                .corporateCompanyNumber(bcCorporationResponse.getInquiryCorporationCompanyNumber())
                                .corporateCompanyLimit(item.getLimit())
                                .bankCode(item.getBankCode())
                                .account(item.getAccount())
                                .paymentDay(23L)
                                .isDeleted(false)
                                .isInstallment(false)
                                .build());

                // 데이터 업데이트
                cardApplicationForm.issuanceStatus(RequestStatusType.APPLY);
                cardApplicationForm.appliedIdxUser(user.idx());
                cardApplicationForm.appliedAt(now);

                cardApplicationForm.corporateCompany(corporateCompany);
                cardApplicationFormDetail.cardLimit(item.getLimit());
                cardApplicationFormDetail.corporateCompanyNumber(corporateCompany.corporateCompanyNumber());
                cardApplicationFormDetail.issuanceStatus(IssuanceStatusType.READY);

                CorporateCompany cc = cardApplicationForm.corporateCompany();
                if(cc.accountType().equals(AccountType.PERSONAL)){
                    BillingUnitLimit bu = billingUnitLimitRepository.findByCorpAndBillingUnitType(cardApplicationForm.corp(), ApplicationType.PRIVATE).orElseThrow( () -> new NotFoundException(GowidMessageGroup.G03001));
                    if(bu.remainLimit() < cardApplicationFormDetail.cardLimit()){
                        log.error("[CAFD Signed ERROR CHECK] [기명 한도 부족]: {}", cardApplicationFormDetail);
                        throw new BadRequestException(GowidMessageGroup.G03001);
                    }else {
                        bu.usedLimit(bu.usedLimit()+cardApplicationFormDetail.cardLimit());
                        bu.remainLimit(bu.totalLimit()-bu.usedLimit());
                        billingUnitLimitRepository.save(bu);
                    }
                }

            } else if (item.getAccountType().equals(AccountType.CORPORATION)) {
                // 법인 계좌
                CorporateCompany corporateCompany = corporateCompanyRepository.findById(item.getIdxCorporateCompany()).orElseThrow(() -> new NotFoundException(GowidMessageGroup.G00005));

                // 데이터 업데이트
                cardApplicationForm.issuanceStatus(RequestStatusType.APPLY);
                cardApplicationForm.appliedIdxUser(user.idx());
                cardApplicationForm.appliedAt(now);

                cardApplicationForm.corporateCompany(corporateCompany);
                cardApplicationFormDetail.cardLimit(item.getLimit());
                cardApplicationFormDetail.issuanceStatus(IssuanceStatusType.READY);
            }

        });

        return true;
    }

    @Transactional
    public Boolean cafdReject(CardApplicationFormDetailApplyReqDto request, CustomUser user) {
        request.getCardApplicationFormDetailApplyList().forEach(item -> {
            CardApplicationFormDetail cardApplicationFormDetail = cardApplicationFormDetailRepository.findById(item.getIdx())
                    .orElseThrow(() -> new NotFoundException(GowidMessageGroup.G00005));

            CardApplicationForm cardApplicationForm = cardApplicationFormDetail.cardApplicationForm();

            cardApplicationForm.issuanceStatus(RequestStatusType.EXPIRED);

            cardApplicationFormDetailRepository.delete(cardApplicationFormDetail);
        });
        return true;
    }

    // 업체 번호 채번
    private BcCorporationResponse getBcCorporationCompanyNumber(String resCompanyIdentityNo) {
        BcCorporationRequest bcCorporationRequest = BcCorporationRequest.builder()
                .businessRegistrationNumber(StringUtils.removeHyphen(resCompanyIdentityNo))
                .issuanceType(BcIssuanceType.NEW)
                .build();
        BcCorporationResponse bcCorporationResponse;
        try {
            bcCorporationResponse = bcFullTextService.requestBcFlf6590(bcCorporationRequest);
        } catch (Exception e) {
            throw new GowidException(ResultCode.BC_ISSUANCE_EXCEPTION);
        }
        return bcCorporationResponse;
    }

    private CafdApplyValidateResDto calcValidation(List<CardApplicationFormDetailApplyDto> corporateDtoList,
                                                   List<CardApplicationFormDetailApplyDto> personalDtoList,
                                                   BillingUnitLimitRes publicBillingUnit,
                                                   BillingUnitLimitRes privateBillingUnit) {
        List<Long> notValidatePrivateIds = new ArrayList<>();
        List<Long> notValidatePublicIds = new ArrayList<>();
        List<Long> result;

        log.info("privateBillingUnit - totalLimit: {}", publicBillingUnit.getTotalLimit());
        log.info("privateBillingUnit - remainLimit: {}", privateBillingUnit.getRemainLimit());
        log.info("privateBillingUnit - usedLimit: {}", privateBillingUnit.getUsedLimit());

        Long personalLimitTotal = personalDtoList.stream()
                .map(CardApplicationFormDetailApplyDto::getLimit)
                .reduce(0L, (a, b) -> a + b);

        if (privateBillingUnit.getTotalLimit() == null || privateBillingUnit.getRemainLimit() == null ||
            (privateBillingUnit.getRemainLimit() != null && privateBillingUnit.getRemainLimit() < personalLimitTotal)) {
            notValidatePrivateIds = personalDtoList.stream().map(CardApplicationFormDetailApplyDto::getIdx).collect(Collectors.toList());
        }

        notValidatePublicIds = corporateDtoList.stream()
                .filter(item -> item.getLimit() > publicBillingUnit.getTotalLimit())
                .map(CardApplicationFormDetailApplyDto::getIdx).collect(Collectors.toList());

        result = Stream.concat(notValidatePublicIds.stream(), notValidatePrivateIds.stream())
                .collect(Collectors.toList());

        return CafdApplyValidateResDto.builder()
                .isValidate(result.isEmpty() ? true : false)
                .ids(result)
                .build();
    }

    public CafdApplyValidateResDto validationCardLimitIncrease(CustomUser user, BcCardLimitIncreaseReqDto request) {

        List<BillingUnitLimitRes> billingUnitLimits = billingUnitLimitService.getBillingUnitLimit(user.corp());

        BillingUnitLimitRes publicBillingUnit = new BillingUnitLimitRes();
        BillingUnitLimitRes privateBillingUnit = new BillingUnitLimitRes();

        for (BillingUnitLimitRes billingUnitLimit : billingUnitLimits) {
            if (billingUnitLimit.getBillingUnitType().equals(ApplicationType.PUBLIC)) {
                publicBillingUnit = billingUnitLimit;
            } else {
                privateBillingUnit = billingUnitLimit;
            }
        }

        List<BcCardLimitIncreaseDto> corporationDtoList = request.getBcCardIncreaseDtoList().stream()
                .filter(item -> item.getAccountType().equals(AccountType.CORPORATION))
                .collect(Collectors.toList());

        List<BcCardLimitIncreaseDto> personalDtoList = request.getBcCardIncreaseDtoList().stream()
                .filter(item -> item.getAccountType().equals(AccountType.PERSONAL))
                .collect(Collectors.toList());


        return calcCardLimitValidation(corporationDtoList, personalDtoList, publicBillingUnit, privateBillingUnit);
    }

    private CafdApplyValidateResDto calcCardLimitValidation(List<BcCardLimitIncreaseDto> corporationDtoList,
                                                            List<BcCardLimitIncreaseDto> personalDtoList,
                                                           BillingUnitLimitRes publicBillingUnit,
                                                           BillingUnitLimitRes privateBillingUnit) {
        Set<Long> notValidatePrivateIds = new HashSet<>();
        List<Long> result;

        Long personalLimitTotal = personalDtoList.stream()
                .map(item -> item.getAfterLimit() - item.getBeforeLimit())
                .reduce(0L, (a, b) -> a + b);

        List<Long> notValidatePublicIds = corporationDtoList.stream()
                .filter(item -> item.getAfterLimit() > publicBillingUnit.getTotalLimit())
                .map(BcCardLimitIncreaseDto::getId).collect(Collectors.toList());

        if (privateBillingUnit != null && privateBillingUnit.getRemainLimit() != null && privateBillingUnit.getRemainLimit() < personalLimitTotal) {
            notValidatePrivateIds = personalDtoList.stream().map(BcCardLimitIncreaseDto::getId).collect(Collectors.toSet());
        }

        for (BcCardLimitIncreaseDto bcCardLimitIncreaseDto : personalDtoList) {
            if (bcCardLimitIncreaseDto.getAfterLimit() > privateBillingUnit.getTotalLimit()) {
                notValidatePrivateIds.add(bcCardLimitIncreaseDto.getId());
            }
        }

        result = Stream.concat(notValidatePublicIds.stream(), notValidatePrivateIds.stream())
                .collect(Collectors.toList());

        return CafdApplyValidateResDto.builder()
                .isValidate(result.isEmpty() ? true : false)
                .ids(result)
                .build();
    }

}
