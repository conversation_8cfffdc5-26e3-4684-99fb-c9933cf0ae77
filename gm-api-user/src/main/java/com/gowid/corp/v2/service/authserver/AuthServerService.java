package com.gowid.corp.v2.service.authserver;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CeoInfo;
import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.corp.ServiceStatus;
import com.gowid.corp.core.domain.corp.ServiceType;
import com.gowid.corp.core.repository.consent.ConsentRepository;
import com.gowid.corp.core.domain.user.OsType;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.domain.user.UserStatus;
import com.gowid.corp.dto.ConsentDto.RegDto;
import com.gowid.corp.dto.FcmTokenReqDto;
import com.gowid.corp.dto.TermsDto.RegistrationDto;
import com.gowid.corp.dto.feign.AuthConsentReqDto;
import com.gowid.corp.dto.feign.AuthCorporationRepresentativeReqDto;
import com.gowid.corp.dto.feign.AuthCorporationReqDto;
import com.gowid.corp.dto.feign.AuthFcmTokenReqDto;
import com.gowid.corp.dto.feign.AuthInvitationUserReqDto;
import com.gowid.corp.dto.feign.AuthProductReqDto;
import com.gowid.corp.dto.feign.AuthRegisterUserReqDto;
import com.gowid.corp.dto.feign.AuthUserConsentReqDto;
import com.gowid.corp.dto.feign.AuthUserPasswordReqDto;
import com.gowid.corp.dto.feign.RepresentativeType;
import com.gowid.corp.dto.internal.InternalUserInviteReqDto;
import com.gowid.corp.dto.internal.InternalUserUpdateReqDto;
import com.gowid.corp.feign.AuthFeignClient;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServerService {

    private final AuthFeignClient authFeignClient;
    private final ConsentRepository consentRepository;

    @Async("gowidExecutor")
    public void createAuthCorporation(String email, Corp corp, JSONObject jsonData) {
        AuthCorporationReqDto reqDto = null;
        try {

            String contactNumber = null;
            if (jsonData != null) {
                if (jsonData.get("resPhoneNo") != null) {
                    contactNumber = jsonData.get("resPhoneNo").toString().trim();
                }
            }

            reqDto = AuthCorporationReqDto.builder()
                .userEmail(email)
                .name(corp.resCompanyNm())
                .nameEng(corp.resCompanyEngNm())
                .jointRepresentativeName(corp.resJointRepresentativeNm())
                .jointIdentityNumber(corp.resJointIdentityNo())
                .businessRegistrationNumber(corp.resCompanyIdentityNo())
                .corporationRegistrationNumber(corp.resUserIdentiyNo())
                .representative(corp.resUserNm())
                .businessItem(corp.resBusinessItems())
                .businessCategory(corp.resBusinessTypes())
                .businessType(corp.resBusinessmanType())
                .openedDate(StringUtils.hasText(corp.resOpenDate()) ?
                    LocalDate.parse(corp.resOpenDate(), DateTimeFormatter.BASIC_ISO_DATE) : null)
                .registeredDate(StringUtils.hasText(corp.resRegisterDate()) ?
                    LocalDate.parse(corp.resRegisterDate(), DateTimeFormatter.BASIC_ISO_DATE) : null)
                .address(corp.resUserAddr())
                .issueNumber(corp.resIssueNo())
                .issueOrganization(corp.resIssueOgzNm())
                .contactNumber(contactNumber)
                .build();
            authFeignClient.registerCorporation(reqDto);
        } catch (Exception e) {
            log.error("[AUTH CORP] 법인 생성에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateCorporationAddress(String businessRegistrationNumber, String address, String addressDetail, String zipCode,
        String contactNumber, String engName, String buildingCode, String fullAddress, String businessCode) {
        AuthCorporationReqDto reqDto = null;
        try {
            reqDto = AuthCorporationReqDto.builder()
                .businessRegistrationNumber(businessRegistrationNumber)
                .address(address)
                .addressDetail(addressDetail)
                .fullAddress(fullAddress)
                .addressZipCode(zipCode)
                .contactNumber(contactNumber)
                .buildingCode(buildingCode)
                .nameEng(engName)
                .businessCode(businessCode)
                .build();
            authFeignClient.updateCorporation(reqDto);
        } catch (Exception e) {
            log.error("[AUTH CORP] 법인 주소 정보 갱신에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateCorporationInfo(Corp corp) {
        AuthCorporationReqDto reqDto = null;
        try {
            reqDto = AuthCorporationReqDto.builder()
                .businessRegistrationNumber(corp.resCompanyIdentityNo())
                .nameEng(corp.resCompanyEngNm())
                .contactNumber(corp.resCompanyNumber())
                .addressZipCode(corp.resCompanyZipCode())
                .address(corp.resCompanyAddr())
                .addressDetail(corp.resCompanyAddrDt())
                .buildingCode(corp.resCompanyBuildingCode())
                .businessCode(corp.resBusinessCode())
                .build();
            authFeignClient.updateCorporation(reqDto);
        } catch (Exception e) {
            log.error("[AUTH CORP] 법인 정보 갱신에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateRepresentativeInfo(String businessRegistrationNumber, String resUserType, Integer ceoCount, CardCompany cardCompany) {
        AuthCorporationReqDto reqDto = null;
        try {
            RepresentativeType representativeType = RepresentativeType.SINGLE;

            if ("2".equals(resUserType)) {
                if (CardCompany.SHINHAN.equals(cardCompany)) {
                    representativeType = RepresentativeType.EACH;
                } else if (CardCompany.LOTTE.equals(cardCompany)) {
                    representativeType = RepresentativeType.JOINT;
                }
            } else if ("3".equals(resUserType)) {
                if (CardCompany.SHINHAN.equals(cardCompany)) {
                    representativeType = RepresentativeType.JOINT;
                } else if (CardCompany.LOTTE.equals(cardCompany)) {
                    representativeType = RepresentativeType.EACH;
                }
            }

            reqDto = AuthCorporationReqDto.builder()
                .businessRegistrationNumber(businessRegistrationNumber)
                .representativeType(representativeType)
                .representativeCount(ceoCount == null ? null : ceoCount.shortValue())
                .build();

            authFeignClient.updateCorporation(reqDto);
        } catch (Exception e) {
            log.error("[AUTH CORP] 법인 대표자 정보 갱신에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void addRepresentative(String email, CeoInfo ceo) {
        AuthCorporationRepresentativeReqDto reqDto = null;
        try {
            reqDto = AuthCorporationRepresentativeReqDto.builder()
                .requesterEmail(email)
                .name(ceo.name())
                .birth(ceo.birth())
                .gender(ceo.genderCode().toString())
                .nationality(ceo.nationality())
                .contactNumber(ceo.phoneNumber())
                .build();
            authFeignClient.addRepresentative(reqDto);
        } catch (Exception e) {
            log.error("[AUTH CORP] 법인 대표자 등록에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void registerUser(User user, List<RegDto> consents, List<Long> businessIds) {
        AuthRegisterUserReqDto dto = null;
        try {
            List<AuthConsentReqDto> consentInfoList = new ArrayList<>();
            // User Consent 정보 변경
            if (!CollectionUtils.isEmpty(consents)) {
                consents.forEach(i -> consentRepository.findById(i.getIdxConsent()).ifPresent(consent ->
                    consentInfoList.add(AuthConsentReqDto.builder()
                        .type(consent.typeCode())
                        .version(consent.version() == null ? 1 : Short.valueOf(consent.version()))
                        .consentOrder((short) consent.consentOrder().longValue())
                        .isAgreed(i.isStatus())
                        .build())
                    )
                );
            }

            dto = AuthRegisterUserReqDto.builder()
                .email(user.email())
                .password(user.password())
                .name(user.name())
                .corporationName(user.corpName())
                .position(user.position())
                .contactNumber(user.mdn())
                .isSmsReceived(user.reception() != null ? user.reception().getIsSendSms() : null)
                .smsConsentedAt(user.smsConsentedAt())
                .isEmailReceived(user.reception() != null ? user.reception().getIsSendEmail() : null)
                .emailConsentedAt(user.emailConsentedAt())
                .consents(consentInfoList)
                .businessIds(new HashSet<>(businessIds))
                .gowidUserId(user.idx())
                .build();

            authFeignClient.registerUser(dto);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 생성에 실패하였습니다. request : {}, message : {}", dto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void addUser(InternalUserInviteReqDto dto, String requesterEmail, Long gowidUserId) {
        AuthInvitationUserReqDto reqDto = null;
        try {
            reqDto = AuthInvitationUserReqDto.builder()
                .requesterEmail(requesterEmail)
                .email(dto.getEmail())
                .roleId(dto.getRoleId())
                .name(dto.getUserName())
                .departmentId(dto.getDepartmentId())
                .position(dto.getPosition())
                .contactNumber(dto.getPhone())
                .isSendMail(dto.getIsSendMail())
                .gowidUserId(gowidUserId)
                .build();

            authFeignClient.addUser(reqDto);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 초대에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateUser(String requester, InternalUserUpdateReqDto dto, UserStatus status) {
        AuthInvitationUserReqDto reqDto = null;
        try {
            reqDto = AuthInvitationUserReqDto.builder()
                .requesterEmail(requester)
                .email(dto.getEmail())
                .roleId(dto.getRoleId())
                .name(dto.getUserName())
                .userName(dto.getUserName())
                .departmentId(dto.getDepartmentId())
                .position(dto.getPosition())
                .contactNumber(dto.getPhone())
                .status(status)
                .build();
            authFeignClient.updateUser(reqDto);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 수정에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateUserPassword(String email, String newPassword) {
        try {
            authFeignClient.updateUserPassword(AuthUserPasswordReqDto.builder()
                .email(email)
                .newPassword(newPassword)
                .build());
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 비밀번호 수정에 실패하였습니다. email : {}, message : {}", email, e.getMessage(), e);
        }
    }


    @Async("gowidExecutor")
    public void addProduct(String businessRegistrationNumber, ServiceType serviceType, ServiceStatus serviceStatus) {
        try {
            authFeignClient.addProduct(AuthProductReqDto.builder()
                    .businessRegistrationNumber(businessRegistrationNumber)
                    .serviceType(serviceType)
                    .serviceStatus(serviceStatus)
                .build());
            authFeignClient.syncRoleCorporation(businessRegistrationNumber);
        } catch (Exception e) {
            log.error("[AUTH PRODUCT] 법인 고위드 제품 데이터 생성에 실패하였습니다. registration number : {}, message : {}", businessRegistrationNumber, e.getMessage(),
                e);
        }
    }

    @Async("gowidExecutor")
    public void updateProduct(String businessRegistrationNumber, ServiceType serviceType, ServiceStatus serviceStatus) {
        try {
            authFeignClient.updateProduct(AuthProductReqDto.builder()
                    .businessRegistrationNumber(businessRegistrationNumber)
                    .serviceType(serviceType)
                    .serviceStatus(serviceStatus)
                .build());
        } catch (Exception e) {
            log.error("[AUTH PRODUCT] 법인 고위드 제품 상태 변경에 실패하였습니다. registration number : {}, message : {}", businessRegistrationNumber, e.getMessage(),
                e);
        }
    }

    @Async("gowidExecutor")
    public void softDeleteUser(String requester, String email) {
        try {
            authFeignClient.deleteUser(requester, email);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 삭제에 실패하였습니다. email : {}, message : {}", email, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateInviteUser(String email) {
        try {
            authFeignClient.updateInvitationUserStatus(email);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 초대 정보 갱신에 실패하였습니다. email : {}, message : {}", email, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    public void updateUserConsent(String email, Boolean isSendSms, LocalDateTime smsConsentedAt,
        Boolean isSendEmail, LocalDateTime emailConsentedAt, List<RegistrationDto> consents) {
        AuthUserConsentReqDto reqDto = null;
        try {
            List<AuthConsentReqDto> consentInfoList = new ArrayList<>();
            // User Consent 정보 변경
            if (!CollectionUtils.isEmpty(consents)) {
                consents.forEach(i -> consentRepository.findById(i.getIdxConsent()).ifPresent(consent ->
                    consentInfoList.add(AuthConsentReqDto.builder()
                        .type(consent.typeCode())
                        .version(consent.version() == null ? 1 : Short.valueOf(consent.version()))
                        .consentOrder((short) consent.consentOrder().longValue())
                        .isAgreed(i.getIsAgreed())
                        .build())
                    )
                );
            }

            reqDto = AuthUserConsentReqDto.builder()
                .email(email)
                .isSendEmail(isSendEmail)
                .emailConsentedAt(emailConsentedAt)
                .isSendSms(isSendSms)
                .smsConsentedAt(smsConsentedAt)
                .consents(consentInfoList)
                .build();
            authFeignClient.updateConsents(reqDto);
        } catch (Exception e) {
            log.error("[AUTH USER] 사용자 약관 정보 갱신에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    // FCM 토큰 등록
    public void addFcmToken(String email, FcmTokenReqDto dto) {
        AuthFcmTokenReqDto reqDto = null;
        try {

            reqDto = AuthFcmTokenReqDto.builder()
                .email(email)
                .osType(dto.getOsType())
                .osVersion(dto.getOsVersion())
                .token(dto.getToken())
                .deviceModel(dto.getDeviceModel())
                .appVersion(dto.getAppVersion())
                .build();
            authFeignClient.saveFcmToken(reqDto);
        } catch (Exception e) {
            log.error("[AUTH FCM TOKEN] 사용자 FCM TOKEN 생성에 실패하였습니다. request : {}, message : {}", reqDto, e.getMessage(), e);
        }
    }

    @Async("gowidExecutor")
    // 로그아웃 시 FCM 토큰 비활성화
    public void updateAllToUnused(String email, OsType osType) {
        try {
            authFeignClient.logout(email, osType);
        } catch (Exception e) {
            log.error("[AUTH FCM TOKEN] 사용자 FCM TOKEN 비활성화에 실패하였습니다. email : {}, os type : {}, message : {}", email, osType, e.getMessage(), e);
        }
    }

}
