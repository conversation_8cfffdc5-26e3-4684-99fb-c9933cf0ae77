package com.gowid.corp.v2.dto.scraping;

import com.gowid.corp.type.AccountDepositType;
import com.gowid.corp.type.AccountStatus;
import com.gowid.corp.type.AccountType;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GoweveAccount {

    private Long id;

    private String registrationNumber;

    private Long certId;

    private String organization;

    private AccountType accountType;

    private String account;

    private BigDecimal balance;

    private String currency;

    private BigDecimal krwBalance;

    private BigDecimal exchangeRate;

    private AccountDepositType accountDeposit;

    private String accountDisplay;

    private LocalDate accountStartDate;

    private LocalDate accountEndDate;

    private String accountName;

    private Boolean isOverdraftAccount;

    private String accountNickname;

    private LocalDate lastTranDate;

    private BigDecimal loanBalance;

    private LocalDate loanStartDate;

    private LocalDate loanEndDate;

    private String loanKind;

    private String loanExecNo;

    private BigDecimal investedCost;

    private BigDecimal earningsRate;

    private AccountStatus status;

    private Integer statusCode;

    private LocalDateTime scrapeDate;

    private String accountHolder;
}
