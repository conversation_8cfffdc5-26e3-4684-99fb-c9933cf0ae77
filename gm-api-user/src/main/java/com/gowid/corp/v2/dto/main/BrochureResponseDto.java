package com.gowid.corp.v2.dto.main;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BrochureResponseDto {

    @Schema(description = "도입문의 신청 응답 아이디")
    private Long id;

    @Schema(description = "회사명")
    @JsonProperty("company")
    private String corporation;

    @Schema(description = "담당자 이름")
    @JsonProperty("name")
    private String name;

    @Schema(description = "회사 이메일")
    @JsonProperty("email")
    private String email;

    @Schema(description = "회사 휴대폰 번호")
    @JsonProperty("phone")
    private String phoneNumber;

    @Schema(description = "투자 유치 여부")
    @JsonProperty("attractInvestment")
    private Boolean hasInvestment;

    @Schema(description = "문의 유형")
    @JsonProperty("category")
    private String typeOfInquiry;

    @Schema(description = "선택약관 동의 여부")
    @JsonProperty("agreement")
    private Boolean isOptionAgreed;
}
