package com.gowid.corp.v2.service.risk;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.v2.dto.limit.LotteCardIssuanceLimitsDto;
import com.gowid.corp.v2.service.card.LotteCardServiceV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@RequiredArgsConstructor
@Service
public class LotteRiskServiceV2 {

    private final LotteCardServiceV2 lotteCardServiceV2;

    public void setLotteRisk(final BigDecimal gowidGrantLimit,
                             final BigDecimal gowidCalculatedLimit,
                             final String hopeLimit,
                             final CardIssuanceInfo cardIssuanceInfo) {
        throwIfNotLotte(cardIssuanceInfo);

        final LotteCardIssuanceLimitsDto lotteCardIssuanceLimitsDto = build(gowidCalculatedLimit, gowidGrantLimit, hopeLimit);

        setLotteFullTexts(cardIssuanceInfo, lotteCardIssuanceLimitsDto);
        setLotteCardIssuanceInfoLimits(cardIssuanceInfo, lotteCardIssuanceLimitsDto);
    }

    // 계산된 한도 CardIssuanceInfo에 저장
    protected void setLotteCardIssuanceInfoLimits(final CardIssuanceInfo cardIssuanceInfo,
                                                  final LotteCardIssuanceLimitsDto lotteCardIssuanceLimitsDto) {
        // 신한카드 별도 계산 로직
        final String calculateLimit = lotteCardIssuanceLimitsDto.getGowidCalculatedLimit();
        final String grantLimit = lotteCardIssuanceLimitsDto.getGowidGrantLimit();

        cardIssuanceInfo.card().calculatedLimit(calculateLimit);
        cardIssuanceInfo.card().grantLimit(grantLimit);
    }

    // 계산된 한도 전문들에 저장
    protected void setLotteFullTexts(final CardIssuanceInfo cardIssuanceInfo,
                                     final LotteCardIssuanceLimitsDto lotteCardIssuanceLimitsDto) {
        lotteCardServiceV2.updateD1100Card(
                cardIssuanceInfo.corp(),
                lotteCardIssuanceLimitsDto.getGowidGrantLimit(),
                lotteCardIssuanceLimitsDto.getGowidCalculatedLimit(),
                lotteCardIssuanceLimitsDto.getHopeLimit()
        );
    }

    protected LotteCardIssuanceLimitsDto build(final BigDecimal gowidCalculatedLimit,
                                               final BigDecimal gowidGrantLimit,
                                               final String hopeLimit) {
        return LotteCardIssuanceLimitsDto.builder()
                .gowidCalculatedLimit(gowidCalculatedLimit.setScale(0).toPlainString())
                .gowidGrantLimit(gowidGrantLimit.setScale(0).toPlainString())
                .hopeLimit(hopeLimit)
                .build();
    }

    protected void throwIfNotLotte(final CardIssuanceInfo cardIssuanceInfo) {
        if (!CardCompany.isLotte(cardIssuanceInfo.cardCompany())) {
            log.error("[롯데카드 리스크 저장 중 오류] 롯데카드 신청정보가 아닙니다. $idxCardIssuanceInfo = {}, cardCompany = {}",
                    cardIssuanceInfo.idx(), cardIssuanceInfo.cardCompany());
            throw new GowidException(ResultCode.BAD_REQUEST);
        }
    }
}
