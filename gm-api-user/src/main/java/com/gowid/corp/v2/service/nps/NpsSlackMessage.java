package com.gowid.corp.v2.service.nps;

import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.v2.dto.nps.SubmitNpsDto;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NpsSlackMessage {

    public static String npsSubmitMessage(final SubmitNpsDto npsDto, final Corp corp, final User user) {
        try {
            StringBuffer sb = new StringBuffer();
            sb.append("NPS 설문 결과").append("\n");
            sb.append(">*법인명* : ").append(corp.resCompanyNm()).append("\n");
            sb.append(">*응답자* : ").append(user.name()).append("(");
            sb.append(String.join(",", user.authorities().stream().map(e -> e.role().name()).collect(Collectors.toList())));
            sb.append(")");
            sb.append("\n");
            sb.append(">*NPS* : ").append(npsDto.getNpsScore()).append("\n");
            sb.append(">*이유* : ").append(npsDto.getUserComment()).append("\n");
            return sb.toString();
        } catch (Exception e) {
            log.error("Failed to create. {}", e.getMessage(), e);
            return "";
        }
    }

}
