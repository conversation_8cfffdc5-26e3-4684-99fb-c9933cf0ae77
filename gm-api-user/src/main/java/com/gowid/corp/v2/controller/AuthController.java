package com.gowid.corp.v2.controller;

import com.gowid.corp.controller.AbstractController;
import com.gowid.corp.core.annotation.CurrentUser;
import com.gowid.corp.core.dto.GowidResponse;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.BusinessResponse;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.exception.LoginFailureException;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.core.utils.RedisConstant;
import com.gowid.corp.dto.AccountDto;
import com.gowid.corp.dto.EmailReqDto;
import com.gowid.corp.dto.FcmTokenReqDto;
import com.gowid.corp.dto.FcmTokenResDto;
import com.gowid.corp.jwt.dto.TokenDto;
import com.gowid.corp.type.VerifyCode;
import com.gowid.corp.v2.dto.AuthDto;
import com.gowid.corp.v2.dto.AuthDto.PasswordBeforeLogin;
import com.gowid.corp.v2.dto.EmailSeedVerifyDto;
import com.gowid.corp.v2.dto.PasswordReqDto;
import com.gowid.corp.v2.dto.SeedResDto;
import com.gowid.corp.v2.dto.UserStatusResDto;
import com.gowid.corp.v2.service.auth.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j

@RestController("AuthV2Controller")
@RequestMapping(AuthController.URI.BASE)
@RequiredArgsConstructor
@Tag(name = "[02] 인증 v2", description = AuthController.URI.BASE)
@Validated
public class AuthController extends AbstractController {

    public static class URI {
        public static final String BASE = "/auth/v2";
        public static final String SEND = "/send";
        public static final String SEND_APP = "/send-app";
        public static final String VERIFY = "/verify";
        public static final String VERIFY_APP = "/verify-app";
        public static final String SEED_VERIFY = "/seed-verify";
        public static final String CHANGE_PASSWORD_BY_LINK = "/password/link";
        public static final String CHANGE_PASSWORD_BY_LINK_APP = "/password/link-app";
        public static final String CHANGE_PASSWORD_BEFORE_LOGIN = "/password/before-login";
        public static final String CHANGE_PASSWORD_BEFORE_LOGIN_APP = "/password/before-login-app";
        public static final String CHANGE_PASSWORD_AFTER_LOGIN = "/password/after-login";
        public static final String TOKEN_ISSUE = "/token/issue";
        public static final String TOKEN_CHECK = "/token/check";
        public static final String CHECK_CURRENT_PASSWORD = "/password/check";
        public static final String STATUS = "/status";
    }

    private final AuthService authService;

    @Operation(summary = "인증코드(4 digits, EMAIL) 발송 요청", description = "" +
            "\n ### Remarks" +
            "\n" +
            "\n - <mark>액세스토큰 불필요</mark>" +
            "\n - 유효시간 : 605s" +
            "\n")
    @GetMapping(URI.SEND)
    @Deprecated
    public GowidResponse<BusinessResponse> sendVerificationCode(@Email(message = "잘못된 이메일 형식입니다.") @RequestParam String email,
                                                  @RequestParam VerifyCode type) {
        return okV1(authService.sendVerificationCode(email, type, null));
    }

    @Operation(summary = "인증코드(4 digits, EMAIL) 발송 요청", description = "" +
            "\n ### Remarks" +
            "\n" +
            "\n - <mark>액세스토큰 불필요</mark>" +
            "\n - 유효시간 : 605s" +
            "\n")
    @PostMapping(URI.SEND)
    public GowidResponse<BusinessResponse> sendMailVerificationCode(@RequestBody EmailReqDto dto) {
        return okV1(authService.sendVerificationCode(dto.getEmail(), dto.getType(), null));
    }

    @Operation(summary = "인증코드(4 digits, EMAIL) 발송 요청", description = "" +
        "\n ### Remarks" +
        "\n" +
        "\n - <mark>액세스토큰 불필요</mark>" +
        "\n - 유효시간 : 185s" +
        "\n")
    @PostMapping(URI.SEND_APP)
    public GowidResponseV2<BusinessResponse> sendMailVerificationCodeApp(@RequestBody EmailReqDto dto) {
        authService.sendVerificationCodeApp(dto.getEmail(), dto.getType());
        return GowidResponseV2.ok();
    }

    @Operation(summary = "인증코드(4 digits, EMAIL) 확인", description = "" +
            "\n ### Remarks" +
            "\n" +
            "\n - <mark>액세스토큰 불필요</mark>" +
            "\n - 인증 후 REGISTER 일 때, 인증번호 삭제됨" +
            "\n"
    )
    @GetMapping(URI.VERIFY)
    public GowidResponseV2<BusinessResponse> checkVerificationCode(
            @RequestParam String email,
            @RequestParam String code,
            @RequestParam VerifyCode verifyType) {
        return ok(authService.checkVerificationCode(email, code, verifyType));
    }

    @Operation(summary = "APP을 위한 인증코드(4 digits, EMAIL) 확인", description = "" +
        "\n ### Remarks" +
        "\n" +
        "\n - <mark>액세스토큰 불필요</mark>" +
        "\n - 인증 후 REGISTER 일 때, 인증번호 삭제됨" +
        "\n"
    )
    @GetMapping(URI.VERIFY_APP)
    public GowidResponseV2<PasswordBeforeLogin> checkVerificationCodeApp(
        @RequestParam String email,
        @RequestParam String code,
        @RequestParam VerifyCode verifyType) {
        return ok(authService.checkVerificationCodeApp(email, code, verifyType));
    }

    @Operation(summary = "seed 유효성 체크")
    @PostMapping(URI.SEED_VERIFY)
    public GowidResponseV2<SeedResDto> checkSeedVerify(@RequestBody EmailSeedVerifyDto dto) {
        return ok(authService.checkSeedVerify(dto));
    }

    @Operation(summary = "이메일 링크를 통한 패스워드 변경")
    @PutMapping(URI.CHANGE_PASSWORD_BY_LINK)
    public GowidResponseV2<Boolean> changePasswordByLink(@Valid @RequestBody PasswordReqDto dto) {
        return ok(authService.changePasswordByLink(dto, RedisConstant.INVITATION_UUID));
    }

    @Operation(summary = "이메일 링크를 통한 패스워드 변경")
    @PutMapping(URI.CHANGE_PASSWORD_BY_LINK_APP)
    public GowidResponseV2<TokenDto.TokenSet> changePasswordByLinkApp(@Valid @RequestBody PasswordReqDto dto) {
        authService.changePasswordByLink(dto, RedisConstant.USER_ACTIVATION_UUID);
        AccountDto accountDto = AccountDto.builder()
            .email(dto.getEmail())
            .password(dto.getNewPassword())
            .osType(dto.getOsType())
            .build();
        return this.loginApp(accountDto);
    }

    @Operation(summary = "비밀번호 변경 - 로그인전", description = "" +
        "\n ### Remarks" +
        "\n - <mark>액세스토큰 불필요</mark>" +
        "\n - 변경 후 인증번호 삭제됨" +
        "\n")
    @PostMapping(URI.CHANGE_PASSWORD_BEFORE_LOGIN)
    public GowidResponse<BusinessResponse> changePasswordPre(@RequestBody AuthDto.PasswordBeforeLogin dto) {
        return okV1(authService.changePasswordBeforeLogin(dto));
    }

    @Operation(summary = "비밀번호 변경 - 로그인전", description = "" +
        "\n ### Remarks" +
        "\n - <mark>액세스토큰 불필요</mark>" +
        "\n - 변경 후 인증번호 삭제됨" +
        "\n")
    @PostMapping(URI.CHANGE_PASSWORD_BEFORE_LOGIN_APP)
    public GowidResponseV2<BusinessResponse> changePasswordPreApp(@RequestBody AuthDto.PasswordBeforeLogin dto) {
        return GowidResponseV2.ok(authService.changePasswordBeforeLoginApp(dto));
    }

    @Operation(summary = "현재 비밀번호 체크", description = "로그인한 사용자의 비밀번호를 변경하기 전 현재 비밀번호를 검증합니다.")
    @PostMapping(URI.CHECK_CURRENT_PASSWORD)
    public GowidResponseV2<Boolean> checkCurrentPassword(@Parameter(hidden = true) @CurrentUser CustomUser customUser,
                                                        @RequestBody AuthDto.PasswordCheck request) {
        return ok(authService.checkCurrentPassword(request.getCurrentPassword(), customUser.user().password()));
    }

    @Operation(summary = "비밀번호 변경 - 로그인후")
    @PostMapping(URI.CHANGE_PASSWORD_AFTER_LOGIN)
    public GowidResponseV2<Boolean> changePasswordAfter(@Parameter(hidden = true) @CurrentUser CustomUser user, @RequestBody AuthDto.PasswordAfterLogin dto) {
        return ok(authService.changePasswordAfterLogin(user.idx(), dto));
    }

    @Operation(summary = "토큰 발급(v2)", description = "" +
            "\n ### Remarks" +
            "\n" +
            "\n - JWT 사용" +
            "\n   - 발급된 토큰 정보를 서버에 저장하지 않음" +
            "\n   - 만료되기 전까지 사용가능하며, 타 기기에서의 다중 로그인도 가능함" +
            "\n   - 개인식별번호(PIN)를 사용한 인증이 어떻게 처리되는지 확인 필요함" +
            "\n     - 최초 로그인 시 인증토큰을 발급하고, 이후 등록된 PIN/TOUCH_ID 인증 통과시 발급된 인증토큰을 사용하는 방식인가?" +
            "\n   - <mark>일단은 현 상태로 두고, 기획의도 확인 후 수정 예정</mark>" +
            "\n - 인증토큰(액세스): 발급 10분 후 만료(현재는 이렇게 되어 있음)" +
            "\n - 인증토큰(갱신): 발급 7일 후 만료(현재는 이렇게 되어 있음)" +
            "\n - 로그인 실패시, 해당 유저가 지출관리 앱 사용자일 경우 별도 결과코드 리턴. " +
            "\n")
    @PostMapping(URI.TOKEN_ISSUE)
    public TokenDto.TokenSet issueTokenSet(@RequestBody AccountDto dto) {
        TokenDto.TokenSet response = authService.issueTokenSet(dto);
        log.info("[로그인] 토큰 발급 완료. email : {}", dto.getEmail());

        CompletableFuture<Boolean> future = authService.updateLoginUser(dto);
        try {
            future.get(2, TimeUnit.SECONDS);
            log.info("[로그인] 로그인 성공 후 사용자 정보 업데이트. email : {}", dto.getEmail());
        } catch (TimeoutException e) {
            log.error("[로그인] 로그인 정보 업데이트 타임 아웃. Error Message : {}, Exception : {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("[로그인] 로그인 정보 업데이트 실패, Error Message : {}, Exception : {}", e.getMessage(), e);
        } finally {
            future.cancel(true);
        }
        return response;
    }

    @PostMapping(URI.TOKEN_CHECK)
    public GowidResponseV2<?> checkValidToken() {
        return ok();
    }

    @Operation(summary = "FCM 토큰 저장")
    @PostMapping(value = "/fcm-token")
    public GowidResponseV2<Boolean> createFcmToken(@Parameter(hidden = true) @CurrentUser CustomUser customUser,
                                                 @RequestBody FcmTokenReqDto fcmTokenReqDto) {
        return ok(authService.createFcmToken(customUser.user(), fcmTokenReqDto));
    }

    @Operation(summary = "사용자 푸시토큰 목록 조회")
    @GetMapping(value = "/fcm-token")
    public GowidResponseV2<List<FcmTokenResDto>> getFcmTokenList(@RequestParam String email) {
        return ok(authService.findFcmTokenList(email));
    }

    @PostMapping(value = "/login-app")
    @Operation(summary = "로그인(앱)", description = "로그인(앱)")
    public GowidResponseV2<TokenDto.TokenSet> loginApp(@RequestBody AccountDto dto) {
        TokenDto.TokenSet response;
        try {
            response = this.issueTokenSet(dto);
        } catch (LoginFailureException e) {
            throw new GowidException(ResultCode.UNAUTHORIZED_INVALID_PARAMETER);
        } catch (GowidException e) {
            if (ResultCode.NOT_EXIST_USER.getCode() == e.getResultCode().getCode() ||
                    ResultCode.INVALID_PARAMETER.getCode() == e.getResultCode().getCode() ||
                    ResultCode.UNAUTHORIZED_INVALID_PASSWORD.getCode() == e.getResultCode().getCode()) {
                throw new GowidException(ResultCode.UNAUTHORIZED_INVALID_PARAMETER);
            }
            throw e;
        }
        return ok(response);
    }

    @PostMapping(value = "/logout")
    @Operation(summary = "로그아웃", description = "로그아웃을 합니다.")
    public GowidResponseV2<Void> logoutApp(@Parameter(hidden = true) @CurrentUser CustomUser customUser, @RequestBody AccountDto dto) {
        /*
        TODO 추후 gowid-back 에 로그아웃 api가 생기면 사용 할 수 있도록 미리 더미 api만 만들어 놓는다. 파라메터로는 token 을 받을 것으로 예상.
         */
        authService.logoutApp(customUser.user(), dto.getOsType());
        return ok();
    }

    @GetMapping(value = URI.STATUS)
    @Operation(summary = "이메일 계정 상태 정보 조회", description = "이메일 계정의 상태 정보를 조회합니다.")
    public GowidResponseV2<UserStatusResDto> getUserStatus(@RequestParam String email) {
        return ok(authService.getUserStatus(email));
    }

}
