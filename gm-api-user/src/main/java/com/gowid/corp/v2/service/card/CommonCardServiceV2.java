package com.gowid.corp.v2.service.card;


import static com.gowid.corp.core.domain.card.CardCompany.getStockRate;
import static com.gowid.corp.v2.service.helper.RiskServiceHelper.toBigDecimal;

import com.gowid.corp.constant.KafkaConstant;
import com.gowid.corp.core.domain.bc.*;
import com.gowid.corp.core.domain.bc.type.ApplicationType;
import com.gowid.corp.core.domain.bc.type.CustomerIdentificationStatusType;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.Card;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.domain.cardIssuanceInfo.CeoInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CeoType;
import com.gowid.corp.core.domain.cardIssuanceInfo.CertificationType;
import com.gowid.corp.core.domain.cardIssuanceInfo.CorpExtend;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceDepth;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceStatus;
import com.gowid.corp.core.domain.cardIssuanceInfo.ManagerInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.Venture;
import com.gowid.corp.core.domain.common.CommonCodeDetail;
import com.gowid.corp.core.domain.common.CommonCodeType;
import com.gowid.corp.core.domain.consent.ConsentMapping;
import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.corp.VentureBusiness;
import com.gowid.corp.core.domain.embed.BankAccount;
import com.gowid.corp.core.domain.lotte.FinancialConsumerProtectionActLotte;
import com.gowid.corp.core.domain.user.UserDetail;
import com.gowid.corp.core.dto.BcCardInfoDto;
import com.gowid.corp.core.dto.response.BcCardInfoResDto;
import com.gowid.corp.core.dto.response.CommonCodeDetailRes;
import com.gowid.corp.core.repository.bc.CardApplicationFormDetailRepository;
import com.gowid.corp.core.repository.bc.CustomerIdentificationRepository;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.common.CommonCodeDetailRepository;
import com.gowid.corp.core.repository.consent.ConsentMappingRepository;
import com.gowid.corp.core.repository.consent.ConsentRepository;
import com.gowid.corp.core.repository.corp.CeoInfoRepository;
import com.gowid.corp.core.repository.corp.ManagerRepository;
import com.gowid.corp.core.repository.corp.VentureBusinessRepository;
import com.gowid.corp.core.repository.querydsl.CustomCardApplicationFormDetailRepository;
import com.gowid.corp.core.repository.shinhan.D1530Repository;
import com.gowid.corp.core.repository.user.UserDetailRepository;
import com.gowid.corp.core.repository.user.UserRepository;
import com.gowid.corp.core.domain.shinhan.D1530;
import com.gowid.corp.core.domain.shinhan.FinancialConsumerProtectionActShinhan;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.dto.GowidResponse;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.encryption.shinhan.Seed128;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.exception.NotFoundException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.core.utils.AESUtil;
import com.gowid.corp.core.utils.Const;
import com.gowid.corp.core.utils.EnvUtil;
import com.gowid.corp.core.utils.GowidUtils;
import com.gowid.corp.domain.FullTextJsonParserForScrape;
import com.gowid.corp.domain.address.AddressInfo;
import com.gowid.corp.dto.BrandConsentDto;
import com.gowid.corp.dto.CardIssuanceDto;
import com.gowid.corp.dto.FinancialConsumersRequestDto;
import com.gowid.corp.dto.FinancialConsumersResponseDto;
import com.gowid.corp.dto.notification.SlackMessageRequestDto;
import com.gowid.corp.dto.salesforce.AccountRegisterReqDto;
import com.gowid.corp.dto.shinhan.DataPart1700;
import com.gowid.corp.exception.AlreadyExistException;
import com.gowid.corp.exception.EntityNotFoundException;
import com.gowid.corp.exception.MismatchedException;
import com.gowid.corp.exception.BadRequestException;
import com.gowid.corp.feign.GoweveScrapeFeignClient;
import com.gowid.corp.secukeypad.EncryptParam;
import com.gowid.corp.secukeypad.SecuKeypad;
import com.gowid.corp.service.CardIssuanceInfoService;
import com.gowid.corp.service.FinancialConsumersService;
import com.gowid.corp.service.bc.CustomerIdentificationService;
import com.gowid.corp.service.bc.FinancialConsumerProtectionActBcService;
import com.gowid.corp.service.lotte.FinancialConsumerProtectionActLotteService;
import com.gowid.corp.service.notification.SlackNotiService;
import com.gowid.corp.service.shinhan.FinancialConsumerProtectionActShinhanService;
import com.gowid.corp.service.shinhan.IssuanceService;
import com.gowid.corp.utils.CommonUtil;
import com.gowid.corp.utils.SalesforceMessage;
import com.gowid.corp.utils.ScrapingResultUtils;
import com.gowid.corp.v2.dto.ScrapingResponse;
import com.gowid.corp.v2.dto.address.response.AddressResponseToSearch;
import com.gowid.corp.v2.dto.card.BcRegisterDto;
import com.gowid.corp.v2.dto.cardissuanceinfo.IssuanceDepthResponseDto;
import com.gowid.corp.v2.dto.scraping.GoweveAccount;
import com.gowid.corp.type.CeoVerifyCode;
import com.gowid.corp.v2.service.address.AddressInfoService;
import com.gowid.corp.v2.service.authserver.AuthServerService;
import com.gowid.corp.v2.service.card.register.RegCommonService;
import com.gowid.corp.v2.service.manager.ManagerInfoService;
import com.gowid.corp.v2.service.risk.CorpRiskService;
import com.gowid.corp.v2.service.scraping.FullTextService;
import com.gowid.corp.utils.CardCommonUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.servlet.http.HttpServletRequest;

import com.gowid.corp.v2.service.scraping.ScrapingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonCardServiceV2 {

    private final UserRepository repoUser;
    private final CardIssuanceInfoRepository repoCardIssuance;
    private final CommonCodeDetailRepository repoCodeDetail;
    private final VentureBusinessRepository repoVenture;
    private final ConsentRepository repoConsent;
    private final ConsentMappingRepository repoConsentMapping;
    private final CeoInfoRepository repoCeo;
    private final ManagerRepository repoManager;
    private final CardApplicationFormDetailRepository cardApplicationFormDetailRepository;
    private final CustomerIdentificationRepository customerIdentificationRepository;
    private final CustomerIdentificationService customerIdentificationService;
    private final IssuanceService issuanceService;

    private final CardIssuanceInfoService cardIssuanceInfoService;
    private final RegCommonService regCommonService;
    private final FullTextService fullTextService;
    private final ManagerInfoService managerInfoService;
    private final CorpRiskService corpRiskService;
    private final BcCardService bcCardService;

    private final ShinhanCardServiceV2 shinhanCardService;
    private final LotteCardServiceV2 lotteCardService;
    private final FinancialConsumersService financialConsumersService;
    private final FinancialConsumerProtectionActShinhanService finConProtectActShinhanService;
    private final FinancialConsumerProtectionActLotteService finConProtectActLotteService;
    private final FinancialConsumerProtectionActBcService finConProtectActBcService;
    private final AuthServerService authServerService;
    private final GoweveCorporateRegisterScrapeService goweveCorporateRegisterScrapeService;
    private final AddressInfoService addressService;
    private final CustomCardApplicationFormDetailRepository customCardApplicationFormDetailRepository;

    private final EnvUtil envUtil;

    private final ModelMapper modelMapper;

    private final GoweveScrapeFeignClient goweveScrapeFeignClient;
    private final SlackNotiService slackNotiService;
    private final UserDetailRepository userDetailRepository;

    private final D1530Repository d1530Repo;

    private final ScrapingService scrapingService;

    @Value("${feign.service.scrape.bank-api-key}")
    private String scrapeBankApiKey;

    @Value("${slack.card-issuance-depth}")
    private String cardIssuanceInfoChannel;

    @Value("${aes.key}")
    private String aesKey;

    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.CorporationRes updateCorporation(Corp corp, CardIssuanceDto.RegisterCorporationInfo dto, CardIssuanceInfo cardInfo) {
        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardInfo.cardCompany())) {
            shinhanCardService.updateD1000Corp(cardInfo, dto);
            shinhanCardService.updateD1400Corp(cardInfo, dto);
        } else if (CardCompany.isLotte(cardInfo.cardCompany())) {
            lotteCardService.updateD1100Corp(corp.idx(), dto);
        }

        corp.resBusinessCode(dto.getBusinessCode());

        return CardIssuanceDto.CorporationRes.from(corp, cardInfo.idx(), null);
    }

    /**
     * 법인정보 업종종류 조회
     *
     * @return 벤처기업사 목록
     */
    @Transactional(readOnly = true)
    public List<CardIssuanceDto.BusinessType> getBusinessType() {
        return repoCodeDetail.findAllByCode(CommonCodeType.BUSINESS_1).stream().map(CardIssuanceDto.BusinessType::from).collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<CommonCodeDetailRes> getCommonCodeDetailByType(CommonCodeType type) {
        return repoCodeDetail.findAllByCode(type).stream().map(CommonCodeDetailRes::from).collect(Collectors.toList());
    }

    /**
     * 법인정보 등록
     *  @param idxUser     등록하는 User idx
     * @param dto         등록정보
     * @param idxCardInfo CardIssuanceInfo idx
     */
    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.CorporationExtendRes updateCorporationExtend(Long idxUser, CardIssuanceDto.RegisterCorporationExtend dto, Long idxCardInfo) {
        User user = findUser(idxUser);

        CardIssuanceInfo cardInfo = cardIssuanceInfoService.findByIdAndIdxUser(idxCardInfo, user.idx());
        if (!cardInfo.idx().equals(idxCardInfo)) {
            throw MismatchedException.builder().category(MismatchedException.Category.CARD_ISSUANCE_INFO).build();
        }

        cardInfo = repoCardIssuance.save(cardInfo.corpExtend(CorpExtend.builder()
                .isVirtualCurrency(dto.getIsVirtualCurrency())
                .isListedCompany(dto.getIsListedCompany())
                .listedCompanyCode(dto.getListedCompanyCode())
                .build())
        );

        if (CardCompany.isLotte(cardInfo.cardCompany())) {
            lotteCardService.updateD1100CorpExtend(user.corp().idx(), dto);
        }

        return CardIssuanceDto.CorporationExtendRes.from(cardInfo, getListedCompanyName(dto.getListedCompanyCode()));
    }

    @Transactional(readOnly = true)
    public List<CardIssuanceDto.CardIssuanceInfoRes> getCardIssuanceInfoList(Long idxUser) {
        User user = findUser(idxUser);
        Corp corp = user.corp();
        Optional<List<CardIssuanceInfo>> cardIssuanceInfos
                = corp != null ? repoCardIssuance.findAllByCorp(corp) : repoCardIssuance.findAllByUser(user);

        // FIXME: 사용하지 않는 모든 동의한 약관 조회 (불필요 쿼리 다수 발생)
        List<CardIssuanceDto.ConsentRes> consents = getConsentRes(idxUser);

        List<CardIssuanceDto.CardIssuanceInfoRes> response =
                Stream.of(CardIssuanceDto.CardIssuanceInfoRes.toDefault(consents)).collect(Collectors.toList());

        String totalBalance = "0";
        if (cardIssuanceInfos.isPresent()) {
            response = cardIssuanceInfos.get()
                    .stream()
                    .map(cardIssuanceInfo -> {

                        List<BcCardInfoDto> bcCardInfoDtoList = null;

                        if (cardIssuanceInfo.cardCompany().equals(CardCompany.BC)) {
                            CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(user.corp(), user, ApplicationType.PUBLIC);

                            if (cardApplicationForm != null) {
                                List<BcCardInfoResDto> bcCardInfoResDtoList = customCardApplicationFormDetailRepository.getBcCardColorTypeAndDeferredTrafficCardDivisionCode(cardApplicationForm);

                                bcCardInfoDtoList = bcCardInfoResDtoList.stream().map(item ->
                                        BcCardInfoDto.builder()
                                                .cardColor(item.getCardColor())
                                                .count(item.getCount())
                                                .isDeferredTrafficCard(item.getDeferredTrafficCard().equals("1") ? true : false)
                                                .build()
                                ).collect(Collectors.toList());
                            } else if (cardApplicationForm == null) {
                                bcCardInfoDtoList = new ArrayList<>();
                            }
                        }


                        // nullable
                        final ManagerInfo managerInfo = managerInfoService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
                        // nuallable
                        final FinancialConsumerProtectionActShinhan finConProtectActShinhan =
                                finConProtectActShinhanService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
                        // nullable
                        final FinancialConsumerProtectionActLotte finConProtectActLotte =
                                finConProtectActLotteService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
                        // nullable
                        final FinancialConsumerProtectionActBc finConProtectActBc =
                                finConProtectActBcService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
                        return CardIssuanceDto.CardIssuanceInfoRes.toDto(
                                totalBalance,
                                cardIssuanceInfo,
                                consents,
                                getCorporationRes(cardIssuanceInfo),
                                CardIssuanceDto.CorporationExtendRes.from(cardIssuanceInfo, getListedCompanyName(!ObjectUtils.isEmpty(cardIssuanceInfo.corpExtend()) ? cardIssuanceInfo.corpExtend().listedCompanyCode() : null)),
                                CardIssuanceDto.AccountRes.from(cardIssuanceInfo, getBankNameOrEmpty(!ObjectUtils.isEmpty(cardIssuanceInfo.bankAccount()) ? cardIssuanceInfo.bankAccount().getBankCode() : null)),
                                managerInfo,
                                finConProtectActShinhan,
                                finConProtectActLotte,
                                finConProtectActBc,
                                bcCardInfoDtoList
                            );
                        }
                    )
                    .collect(Collectors.toList());
        }

        return response;
    }

    private String getListedCompanyName(String ListedCompanyCode) {
        CommonCodeDetail commonCodeDetail = repoCodeDetail.getByCodeAndCode1(CommonCodeType.LOTTE_LISTED_EXCHANGE, ListedCompanyCode);
        if (ObjectUtils.isEmpty(commonCodeDetail)) {
            return null;
        }
        return commonCodeDetail.value1();
    }

    private List<CardIssuanceDto.ConsentRes> getConsentRes(Long idx_user) {
        List<CardIssuanceDto.ConsentRes> consentInfo = new ArrayList<>();

        List<BrandConsentDto> consents = repoConsent.findAllByEnabledOrderByConsentOrderAsc(true)
                .map(BrandConsentDto::from)
                .collect(Collectors.toList());

        consents.forEach(item -> {
            ConsentMapping consentMapping = repoConsentMapping.findTopByIdxUserAndIdxConsentOrderByIdxDesc(idx_user, item.getIdx());

            CardIssuanceDto.ConsentRes resTemp = CardIssuanceDto.ConsentRes.builder()
                    .consentIdx(item.getIdx())
                    .title(item.getTitle())
                    .boolConsent(consentMapping != null && consentMapping.status())
                    .consentType(item.getTypeCode())
                    .essential(item.getEssential())
                    .build();
            consentInfo.add(resTemp);
        });

        return consentInfo;
    }

    private CardIssuanceDto.CorporationRes getCorporationRes(CardIssuanceInfo cardIssuanceInfo) {
        if (ObjectUtils.isEmpty(cardIssuanceInfo.corp())) {
            return null;
        }

        CardIssuanceDto.CorporationRes corporationRes = CardIssuanceDto.CorporationRes.from(cardIssuanceInfo.corp(), cardIssuanceInfo.idx(), cardIssuanceInfo);
        if (!ObjectUtils.isEmpty(corporationRes.getBusinessCode())) {
            CommonCodeDetail codeDetailData = repoCodeDetail.getByCode1AndCode5(corporationRes.getBusinessCode().substring(0, 1), corporationRes.getBusinessCode().substring(1));
            corporationRes.setBusinessCodeValue(codeDetailData.value5());
        }
        return corporationRes;
    }

    // 레거시 코드 기준 파라미터가 없는 경우 다수 존재
    public String getBankNameOrEmpty(@Nullable String bankCode) {
        if (!StringUtils.hasText(bankCode)) {
            return "";
        }

        return regCommonService.getOrganizationNameOrEmpty(bankCode);
    }

    /**
     * 벤처기업사 조회
     *
     * @return 벤처기업사 목록
     */
    @Transactional(readOnly = true)
    public List<String> getVentureBusiness() {
        return repoVenture.findAllByOrderByNameAsc().stream().map(VentureBusiness::name).collect(Collectors.toList());
    }

    /**
     * 벤처기업정보 등록
     *
     * @param idxUser     등록하는 User idx
     * @param dto         등록정보
     * @param idxCardInfo CardIssuanceInfo idx
     * @return 등록 정보
     */
    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.VentureRes registerVenture(Long idxUser, CardIssuanceDto.RegisterVenture dto, Long idxCardInfo) {
        User user = findUser(idxUser);
        CardIssuanceInfo cardInfo = cardIssuanceInfoService.findByIdAndIdxUser(idxCardInfo, user.idx());
        if (!cardInfo.idx().equals(idxCardInfo)) {
            log.error("CardIssuanceInfo is not equals. idxCardInfo: {}, cardInfo.idx(): {}", idxCardInfo, cardInfo.idx());
            throw new NotFoundException(GowidMessageGroup.G00005);
        }

        String investorName = repoVenture.findEqualsName(dto.getInvestorName());
        cardInfo.venture(Venture.builder()
                .investAmount(dto.getAmount())
                .isVC(dto.getIsVC())
                .isVerifiedVenture(dto.getIsVerifiedVenture())
                .investor(investorName != null ? investorName : dto.getInvestorName())
                .isExist(investorName != null)
                .build()
        );

        corpRiskService.updateVentureInfo(user.corp().idx(), dto.getIsVerifiedVenture(), dto.getIsVC(), null);
        return CardIssuanceDto.VentureRes.from(repoCardIssuance.save(cardInfo));
    }

    /**
     * 카드 희망한도 저장
     *
     * @param idxUser 등록하는 User idx
     * @param dto     등록정보
     * @return 등록 정보
     */
    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.CardRes saveHopeLimit(Long idxUser, CardIssuanceDto.HopeLimitReq dto) {
        User user = findUser(idxUser);

        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findByUserAndCardTypeOrDefaultEntity(user,
                dto.getCardType() != null ? dto.getCardType() : CardType.GOWID,
                CardIssuanceInfo.builder()
                        .corp(user.corp())
                        .user(user)
                        .cardType(dto.getCardType() != null ? dto.getCardType() : CardType.GOWID)
                        .issuanceStatus(IssuanceStatus.INPROGRESS)
                        .build()
        );

        // TODO: 비정상 요청으로 인한 NPE 발생 가능성 임시 방지.
        //       추후 원인 파악 후 삭제 필요
        if (ObjectUtils.isEmpty(cardIssuanceInfo.card()))
            throw new NotFoundException(GowidMessageGroup.G02000.getMessage());

        Card card = cardIssuanceInfo.card();

        String hopeLimit = dto.getHopeLimit();
        card.hopeLimit(hopeLimit);
        cardIssuanceInfo.card(card);
        String grantLimit = card.grantLimit();

        if (StringUtils.hasText(grantLimit)) {
            calculateGrantLimit(user, cardIssuanceInfo, card);
        }

        repoCardIssuance.save(cardIssuanceInfo);
        return CardIssuanceDto.CardRes.from(cardIssuanceInfo, null);
    }

    private void calculateGrantLimit(User user, CardIssuanceInfo cardIssuanceInfo, Card card) {
        String calculatedLimit = card.calculatedLimit();
        Long calculatedLimitLong = Long.parseLong(calculatedLimit);

        String hopeLimit = card.hopeLimit();
        Long hopeLimitLong = Long.parseLong(hopeLimit);

        String grantLimit = calculatedLimitLong > hopeLimitLong ? hopeLimit : calculatedLimit;
        CardCompany cardCompany = cardIssuanceInfo.cardCompany();

        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardCompany)) {
            grantLimit = calculateShinhanGrantLimit(toBigDecimal(grantLimit, 0, RoundingMode.DOWN));
            shinhanCardService.updateShinhanFulltextLimit(cardIssuanceInfo, grantLimit);
        } else if (CardCompany.isLotte(cardCompany)) {
            lotteCardService.updateD1100Limit(user, grantLimit, hopeLimit);
        }
        card.grantLimit(grantLimit);
    }

    // 고위드 부여한도와 신한카드 최대 한도 비교
    public String calculateShinhanGrantLimit(final BigDecimal gowidInitGrantLimit) {
        CommonCodeDetail shinhanContractDetail = repoCodeDetail.getByCodeAndCode1(CommonCodeType.CARD_LIMIT, CardCompany.SHINHAN.getName());
        final BigDecimal shinhanMaxGrantLimit = toBigDecimal(shinhanContractDetail.value1(), 0, RoundingMode.DOWN);

        return gowidInitGrantLimit.min(shinhanMaxGrantLimit).toPlainString();
    }

    /**
     * 결제 계좌정보 등록
     *
     * @param idxUser     등록하는 User idx
     * @param dto         등록정보
     * @param idxCardInfo CardIssuanceInfo idx
     * @return 등록 정보
     */
    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.AccountRes registerAccount(Long idxUser, CardIssuanceDto.RegisterAccount dto, Long idxCardInfo) {
        User user = findUser(idxUser);
        CardIssuanceInfo cardInfo = cardIssuanceInfoService.findByIdAndIdxUser(idxCardInfo, user.idx());
        if (!cardInfo.idx().equals(idxCardInfo)) {
            throw MismatchedException.builder().category(MismatchedException.Category.CARD_ISSUANCE_INFO).build();
        }

        GoweveAccount account = findGoweveAccount(cardInfo.corp().resCompanyIdentityNo(), dto.getAccountIdx());
        if (ObjectUtils.isEmpty(account.getAccountHolder())) {
            account.setAccountHolder(dto.getAccountHolder());
        }

        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardInfo.cardCompany())) {
            shinhanCardService.updateD1100Account(cardInfo, account);
        } else if (CardCompany.isLotte(cardInfo.cardCompany())) {
            lotteCardService.updateD1100Account(user.corp().idx(), account);
        } else if (CardCompany.isBC(cardInfo.cardCompany())) {
            CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(user.corp(), user, ApplicationType.PUBLIC);
            CorporateCompany corporateCompany = cardApplicationForm.corporateCompany();

            corporateCompany
                    .account(account.getAccount())
                    .accountNickName(account.getAccountNickname())
                    .paymentDay(23L)
                    .bankCode(account.getOrganization());
        }

        cardInfo.bankAccount(BankAccount.builder()
                .bankAccount(account.getAccount())
                .bankCode(GowidUtils.get3digitsBankCode(account.getOrganization()))
                .bankAccountHolder(account.getAccountHolder())
                .build());

        return CardIssuanceDto.AccountRes.from(repoCardIssuance.save(cardInfo), getBankNameOrEmpty(account.getOrganization()));
    }

    private GoweveAccount findGoweveAccount(String registrationNumber, Long idxResAccount) {
        GowidResponse<GoweveAccount> response = goweveScrapeFeignClient.getAccount(registrationNumber, idxResAccount, scrapeBankApiKey);
        if (response.getData().getId() == null) {
            throw new NotFoundException(GowidMessageGroup.G03002);
        }
        return response.getData();
    }

    /**
     * 대표자 정보
     *
     * @param idxUser 조회하는 User idx
     * @return 등록 정보
     */
    @Transactional(readOnly = true)
    public CardIssuanceDto.CeoTypeRes getCeoType(Long idxUser, Long idxCardIssuanceInfo) {
        User user = repoUser.findById(idxUser).orElseThrow(() -> EntityNotFoundException.builder().entity("User").build());

        if (user.corp() == null) {
            throw EntityNotFoundException.builder().entity("Corp").build();
        }

        Integer count = user.corp().ceoCount();
        if (count == null) {
            throw new com.gowid.corp.core.exception.BadRequestException("법인의 대표자 수 값이 존재하지 않습니다.");
        }

        String ceoTypeString = user.corp().resUserType();
        if (ceoTypeString == null) {
            throw new com.gowid.corp.core.exception.BadRequestException("법인의 대표자 유형 값이 존재하지 않습니다.");
        }

        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findById(idxCardIssuanceInfo);
        CeoType ceoType = CeoType.SINGLE;

        if (CardCompany.SHINHAN.equals(cardIssuanceInfo.cardCompany())) {
            ceoType = CeoType.fromShinhan(ceoTypeString);
        } else if (CardCompany.LOTTE.equals(cardIssuanceInfo.cardCompany())) {
            ceoType = CeoType.fromLotte(ceoTypeString);
        } else if (CardCompany.BC.equals(cardIssuanceInfo.cardCompany())) {
            ceoType = CeoType.fromBc(ceoTypeString);
        }

        return CardIssuanceDto.CeoTypeRes.builder()
                .type(ceoType)
                .count(count)
                .build();
    }

    /**
     * 대표자 등록
     *
     * @param idxUser     등록하는 User idx
     * @param dto         등록정보
     * @param idxCardInfo CardIssuanceInfo idx
     * @return 등록 정보
     */
    @Transactional(rollbackFor = Exception.class)
    public CardIssuanceDto.CeoRes registerCeo(Long idxUser, CardIssuanceDto.RegisterCeo dto, Long idxCardInfo) {
        User user = findUser(idxUser);
        CardIssuanceInfo cardInfo = cardIssuanceInfoService.findById(idxCardInfo);
        if (!cardInfo.idx().equals(idxCardInfo)) {
            throw MismatchedException.builder().category(MismatchedException.Category.CARD_ISSUANCE_INFO).build();
        }

        List<CeoInfo> ceoInfos = repoCeo.getByCardIssuanceInfo(cardInfo);
        if (!ceoInfos.isEmpty()) {
            for (CeoInfo ceoInfo : ceoInfos) {
                // ceo중복등록 예외처리
                if (StringUtils.hasText(ceoInfo.name()) && ((ceoInfo.name().equals(dto.getName()) || ceoInfo.engName().equals(dto.getEngName()))
                        && ceoInfo.phoneNumber() != null)) {
                    throw AlreadyExistException.builder().category("ceo")
                            .resource(dto.getName())
                            .build();
                }
            }
        }

        CeoInfo ceo = null;
        Integer ceoNum = 0;
        if (!ObjectUtils.isEmpty(dto.getCeoIdx())) {
            ceo = findCeoInfo(dto.getCeoIdx());
            if (!cardInfo.ceoInfos().contains(ceo)) {
                // ceo업데이트중 해당 ceo정보가 있으나 cardInfo에 매칭되지 않는 경우
                throw MismatchedException.builder().category(MismatchedException.Category.CEO).build();
            }
            if (ceo.phoneNumber() != null) {
                // ceo중복등록 예외처리
                // 초기등록시 신분증진위확인만 진행하고 등록됨
                // 따라서 핸드폰번호가 null이 아닌 경우 중복등록으로 처리
                throw AlreadyExistException.builder().category("ceo")
                        .resource(dto.getName())
                        .build();
            }
            if (ceo.ceoNumber() > 0) {
                ceoNum = ceo.ceoNumber();
            }
        }
        Boolean addrState = true;
        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardInfo.cardCompany())) {
            ceo = shinhanCardService.updateCeo(ceo, cardInfo, dto, ceoNum);
        } else if (CardCompany.isLotte(cardInfo.cardCompany())) {
            ceo = lotteCardService.updateCeo(ceo, user.corp().idx(), cardInfo, dto, ceoNum);
        } else if (CardCompany.isBC(cardInfo.cardCompany())) {
            ceo = ceoInfos.get(ceoInfos.size()-1);
//            JSONArray corpRegisterCeoInfo = getCorpRegisterCeoInfoFromScarpingServer(user.corp().resUserIdentiyNo());
//            List<String> listResCeoList = FullTextJsonParserForScrape.parseCeoListInJsonFormatByCeoName(corpRegisterCeoInfo, dto.getName());
            bcCardService.updateCeo(ceo, user.corp().idx(), cardInfo, dto, ceoNum);

//            if (!fullAddress.isEmpty()) {
//                String[] fullAddressList = fullAddress.split(", ");
//                String[] resultAddress = {fullAddressList[0], String.join(", ", Arrays.copyOfRange(fullAddressList, 1, fullAddressList.length))};
//
//                String zipNo = addressResponse.getResults().getJuso().get(0).getZipNo();
//                ceo.ceoAddressBasic(resultAddress[0]);
//                ceo.ceoAddressDetail(resultAddress[1]);
//                ceo.ceoZipCode(zipNo);
//                ceo.ceoAddressDivisionCode(zipNo.length() == 5 ? "R" : "J");
//                ceo.ceoAddressRoadNameCode(addressResponse.getResults().getJuso().get(0).getRnMgtSn());
//            }
        }

        if (CardCommonUtils.isStockholderUpdateCeo(cardInfo)) {
            setStockholderByCeoInfo(cardInfo, ceo, getStockRate(cardInfo.cardCompany()));
        }

        SalesforceMessage.publish(KafkaConstant.SALESFORCE_ACCOUNT_UPSERT, AccountRegisterReqDto.cardIssuance(cardInfo.idx()));
        CardIssuanceDto.CeoRes response;
        response = CardIssuanceDto.CeoRes.from(repoCeo.save(ceo)).setDeviceId("");
        authServerService.addRepresentative(user.email(), ceo);

        try {
            final StringBuilder builder = new StringBuilder();
            builder.append("계정 : ").append(user.email()).append("\n")
                .append("단계 : ").append("[14] 대표자인증 (인증성공)").append("\n")
                .append("카드사 : ").append(cardInfo.cardCompany().getName()).append("\n")
                .append("법인명 : ").append(user.corp().resCompanyNm()).append("\n")
                .append("대표자 타입 : ").append(cardInfo.getCeoType().getDescription());

            slackNotiService.sendSlackMessage(SlackMessageRequestDto.basic(cardIssuanceInfoChannel, builder.toString()));
        } catch (Exception e) {
            log.error("[대표자 슬랙 발송 오류] $idxUser={} $입력된 대표자 정보 dto={} $idxCardInfo={}, $error={}", idxUser, dto, idxCardInfo, e);
        }

        return response;
    }

    private CeoInfo findCeoInfo(Long idxCeo) {
        return repoCeo.findById(idxCeo).orElseThrow(
                () -> EntityNotFoundException.builder()
                        .entity("CeoInfo")
                        .idx(idxCeo)
                        .build()
        );
    }

    private CardIssuanceInfo setStockholderByCeoInfo(CardIssuanceInfo cardIssuanceInfo, CeoInfo ceoInfo, String stockRate) {
        return cardIssuanceInfo.stockholder(cardIssuanceInfo.stockholder()
                .stockholderName(ceoInfo.name())
                .stockholderEngName(ceoInfo.engName())
                .stockholderBirth(ceoInfo.birth())
                .stockholderNation(ceoInfo.nationality())
                .stockRate(stockRate));
    }

    /**
     * 관리책임자 변경
     *
     * @param idxUser             등록하는 User idx
     * @param registerManagerDto  등록 정보
     * @param idxCardIssuanceInfo CardIssuanceInfo idx
     * @return 등록 정보
     */
    @Transactional
    @Deprecated
    public CardIssuanceDto.ManagerRes registerManagerLegacy(Long idxUser, CardIssuanceDto.RegisterManager registerManagerDto, Long idxCardIssuanceInfo) {
        log.debug("[관리자 정보 업데이트] $idxUser={} $입력된 관리자 정보 dto={} $idxCardInfo={}", idxUser, registerManagerDto, idxCardIssuanceInfo);

        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findByIdAndIdxUser(idxCardIssuanceInfo, idxUser);

        try {
            // nullable
            ManagerInfo managerInfo = managerInfoService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
            managerInfo = updateOrSaveManagerInfo(managerInfo, registerManagerDto, cardIssuanceInfo);

            updateFullTextManager(cardIssuanceInfo, cardIssuanceInfo.user(), registerManagerDto);
            SalesforceMessage.publish(KafkaConstant.SALESFORCE_ACCOUNT_UPSERT, AccountRegisterReqDto.cardIssuance(cardIssuanceInfo.idx()));

            return CardIssuanceDto.ManagerRes.from(managerInfo);
        } catch (Exception e) {
            log.error("[관리자 정보 업데이트] 오류 발생 $idxUser={} $입력된 관리자 정보 dto={} $idxCardInfo={}, $error={}", idxUser, registerManagerDto, idxCardIssuanceInfo, e);
            throw new BadRequestException(GowidMessageGroup.G02000);
        }
    }

    /**
     * 관리책임자 변경
     *
     * @param idxUser             등록하는 User idx
     * @param registerManagerDto  등록 정보
     * @param idxCardIssuanceInfo CardIssuanceInfo idx
     * @return 등록 정보
     */
    @Transactional
    public CardIssuanceDto.ManagerRes registerManager(Long idxUser, CardIssuanceDto.RegisterManager registerManagerDto, Long idxCardIssuanceInfo) {
        log.debug("[관리자 정보 업데이트] $idxUser={} $입력된 관리자 정보 dto={} $idxCardInfo={}", idxUser, registerManagerDto, idxCardIssuanceInfo);

        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findByIdAndIdxUser(idxCardIssuanceInfo, idxUser);
        try {
            // nullable
            ManagerInfo managerInfo = managerInfoService.getByIdxCardIssuanceInfoOrNull(cardIssuanceInfo.idx());
            managerInfo = updateOrSaveManagerInfo(managerInfo, registerManagerDto, cardIssuanceInfo);

            updateFullTextManager(cardIssuanceInfo, cardIssuanceInfo.user(), registerManagerDto);
            SalesforceMessage.publish(KafkaConstant.SALESFORCE_ACCOUNT_UPSERT, AccountRegisterReqDto.cardIssuance(cardIssuanceInfo.idx()));

            final User user = repoUser.findById(idxUser)
                    .orElseThrow(() -> new GowidException(ResultCode.BAD_REQUEST, "유저를 찾을 수 없습니다."));

            final StringBuilder builder = new StringBuilder();
            builder.append("계정 : ").append(user.email()).append("\n");
            if ("0".equals(registerManagerDto.getCeoNumber())) {
                builder.append("단계 : ").append("[14] 카드관리자 등록 - 직접입력").append("\n");
            } else {
                builder.append("단계 : ").append("[14] 카드관리자 등록 - 대표자" + registerManagerDto.getCeoNumber()).append("\n");
            }

            builder.append("카드사 : ").append(cardIssuanceInfo.cardCompany().getName()).append("\n")
                .append("법인명 : ").append(user.corp().resCompanyNm()).append("\n")
                .append("대표자 타입 : ").append(cardIssuanceInfo.getCeoType().getDescription());
            slackNotiService.sendSlackMessage(SlackMessageRequestDto.basic(cardIssuanceInfoChannel, builder.toString()));

            // BC카드인 경우
            if (cardIssuanceInfo.cardCompany().equals(CardCompany.BC)) {
                CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(user.corp(), user, ApplicationType.PUBLIC);
                CustomerIdentification customerIdentification = cardApplicationForm.customerIdentification();

                // 카드관리자를 따로 설정할 경우
                if (!StringUtils.isEmpty(registerManagerDto.getDepartment()) && !StringUtils.isEmpty(registerManagerDto.getTitle())) {
                    LocalDateTime nowLocalDateTime = LocalDateTime.now();

                    customerIdentification
                            .customerType("07")     // 무기명 최초일 경우 대리인으로 처리
                            .korName(registerManagerDto.getName())
                            .engName(registerManagerDto.getEngName())
                            .address(user.corp().resCompanyAddr() + ", " + user.corp().resCompanyAddrDt())
                            .mobilePhoneNumber(registerManagerDto.getPhoneNumber())
                            .email(user.email())
                            .realNameCertificate(CertificationType.convertBcCustomerIdentificationToCertificationType(managerInfo.certificationType()))
                            .issueDate(managerInfo.identificationIssuedDate())
                            .nationality(managerInfo.nation())
                            .identityNumber(managerInfo.identificationNumber())
                            .drivingLicenseNumber(managerInfo.drivingLicenseNumber())
                            .state(CustomerIdentificationStatusType.WAIT);

                    log.info("[CUSTOMER IDENTITY INFO] : {}", customerIdentification.toString());
                    customerIdentificationRepository.save(customerIdentification);

                    customerIdentificationService.makeCustomerIdentificationPdf(customerIdentification, user.corp().resCompanyIdentityNo(), idxCardIssuanceInfo, idxUser);
                }
                // 카드관리자를 대표로 설정할 경우
                else if (!StringUtils.isEmpty(registerManagerDto.getCeoNumber()) && !"0".equals(registerManagerDto.getCeoNumber())) {
                    // 대표자 정보 가져오기
                    List<CeoInfo> ceoInfos = repoCeo.getByCardIssuanceInfo(cardIssuanceInfo);
                    if (!ceoInfos.isEmpty()) {
                        // 설정한 대표자 번호에 해당하는 대표자 정보 찾기
                        int ceoIndex = Integer.parseInt(registerManagerDto.getCeoNumber()) - 1;
                        if (ceoIndex >= 0 && ceoIndex < ceoInfos.size()) {
                            CeoInfo ceoInfo = ceoInfos.get(ceoIndex);

                            customerIdentification
                                    .customerType("07")     // 무기명 최초일 경우 대리인으로 처리
                                    .korName(ceoInfo.name())
                                    .engName(ceoInfo.engName())
                                    .address(user.corp().resCompanyAddr() + ", " + user.corp().resCompanyAddrDt())
                                    .mobilePhoneNumber(ceoInfo.phoneNumber())
                                    .email(user.email())
                                    .realNameCertificate(CertificationType.convertBcCustomerIdentificationToCertificationType(ceoInfo.certificationType()))
                                    .issueDate(ceoInfo.identificationIssuedDate())
                                    .nationality(ceoInfo.nationality())
                                    .identityNumber(ceoInfo.identificationNumber())
                                    .drivingLicenseNumber(ceoInfo.drivingLicenseNumber())
                                    .state(CustomerIdentificationStatusType.WAIT);

                            log.info("[CUSTOMER IDENTITY INFO - 카드관리자가 대표자인 케이스] : {}", customerIdentification.toString());
                            customerIdentificationRepository.save(customerIdentification);

                            customerIdentificationService.makeCustomerIdentificationPdf(customerIdentification, user.corp().resCompanyIdentityNo(), idxCardIssuanceInfo, idxUser);
                        }
                    } else {
                        log.error("[CUSTOMER IDENTITY INFO] : [BC카드] 카드관리자를 대표로 설정하고 고객 거래 확인서를 생성하려고 했으나 대표자 정보를 찾지 못했습니다.\n{}", builder.toString());
                        slackNotiService.sendSlackMessage(SlackMessageRequestDto.basic(cardIssuanceInfoChannel,
                                "[BC카드] 카드관리자를 대표로 설정하고 고객 거래 확인서를 생성하려고 했으나 대표자 정보를 찾지 못했습니다.\n" + builder.toString()));
                    }
                }
                // 그 외의 경우
                else {
                    log.info("매니저 세팅 : {}", managerInfo);
                    cardApplicationForm.managerInfo(managerInfo);
                }

            }

            return CardIssuanceDto.ManagerRes.from(managerInfo);
        } catch (Exception e) {
            log.error("[관리자 정보 업데이트] 오류 발생 $idxUser={} $입력된 관리자 정보 dto={} $idxCardInfo={}, $error={}", idxUser, registerManagerDto, idxCardIssuanceInfo, e);
            throw new BadRequestException(GowidMessageGroup.G02000);
        }
    }

    public ManagerInfo updateOrSaveManagerInfo(ManagerInfo managerInfo, CardIssuanceDto.RegisterManager registerManagerDto, CardIssuanceInfo cardIssuanceInfo) {
        if (!ObjectUtils.isEmpty(managerInfo)) {
            modelMapper.getConfiguration().setSkipNullEnabled(true);
            modelMapper.map(registerManagerDto, managerInfo);
            return managerInfo;
        } else {
            ManagerInfo saveManagerInfo = ManagerInfo.builder()
                    .cardIssuanceInfo(cardIssuanceInfo)
                    .engName(registerManagerDto.getEngName())
                    .name(registerManagerDto.getName())
                    .nation(registerManagerDto.getNation())
                    .phoneNumber(registerManagerDto.getPhoneNumber())
                    .genderCode(registerManagerDto.getGenderCode())
                    .birth(registerManagerDto.getBirth())
                    .build();
            return repoManager.save(saveManagerInfo);
        }
    }

    public void updateFullTextManager(CardIssuanceInfo cardIssuanceInfo, User user, CardIssuanceDto.RegisterManager registerManagerDto) {
        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardIssuanceInfo.cardCompany())) {
            shinhanCardService.updateManager(user, cardIssuanceInfo, registerManagerDto);
        } else if (CardCompany.isLotte(cardIssuanceInfo.cardCompany())) {
            lotteCardService.updateManager(user, registerManagerDto);
        }
    }

    /**
     * 카드 신청
     * <p>
     * 1700 신분증 위조확인
     */
    @Transactional
    public void verifyRepresentativeIdentification(HttpServletRequest request, Long idxUser, CardIssuanceDto.IdentificationReq dto) {
        User user = findUser(idxUser);
        dto.validate();
        String driverLocal = dto.getDriverLocal();

        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findById(dto.getCardIssuanceInfoIdx());
        if (!cardIssuanceInfo.idx().equals(dto.getCardIssuanceInfoIdx())) {
            throw new GowidException(ResultCode.BAD_REQUEST, "카드 발급 정보가 일치하지 않습니다.");
        }

        String[] encryptParams;
        if (CertificationType.DRIVER.equals(dto.getIdentityType())) {
            encryptParams = new String[]{EncryptParam.IDENTIFICATION_NUMBER, EncryptParam.DRIVER_NUMBER};
            dto.setDriverLocal(findShinhanDriverLocalCode(dto.getDriverLocal()));
        } else {
            encryptParams = new String[]{EncryptParam.IDENTIFICATION_NUMBER};
        }

        Map<String, String> decryptData;
        try {
            decryptData = SecuKeypad.decrypt(request, dto.getEncryptData(), encryptParams);
        } catch (Exception e) {
            log.error("[KEYPAD] 키패드 에러 message : {}", e.getMessage(), e);
            throw new GowidException(ResultCode.SECURITY_KEYPAD_EXCEPTION, "서버 오류가 발생했습니다. 고객센터로 문의주세요.");
        }

        verifyRepresentative(idxUser, dto, decryptData);

        if (envUtil.isProd() && !"0".equals(dto.getCeoSeqNo())) {
            // 실제 법인 ceo가 맞는지 등기부등본(d1000)과 비교확인
//            verifyCorrespondRepresentative(cardIssuanceInfo.corp().idx(), CardIssuanceDto.CeoValidReq.builder()
//                    .identificationNumberFront(dto.getIdentificationNumberFront())
//                    .name(dto.getKorName())
//                    .nation(dto.getNation()).build());

            verifyCorrespondRepresentativeByScrap(user.corp(), CardIssuanceDto.CeoValidReq.builder()
                    .identificationNumberFront(dto.getIdentificationNumberFront())
                    .name(dto.getKorName())
                    .nation(dto.getNation()).build());
        }

        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardIssuanceInfo.cardCompany())) {
            shinhanCardService.updateRepresentative(cardIssuanceInfo, dto, decryptData);
        } else if (CardCompany.isLotte(cardIssuanceInfo.cardCompany())) {
            lotteCardService.updateRepresentative(cardIssuanceInfo, dto, decryptData);
        } else if (CardCompany.isBC(cardIssuanceInfo.cardCompany())) {
            updateManagerInfo(dto, user, cardIssuanceInfo, decryptData, driverLocal);
        }
    }

    private void updateManagerInfo(CardIssuanceDto.IdentificationReq dto, User user, CardIssuanceInfo cardIssuanceInfo, Map<String, String> decryptData, String driverLocal) {
        CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(user.corp(), user, ApplicationType.PUBLIC);

        String identificationNumber = AESUtil.encryptWithCertificationServer(dto.getIdentificationNumberFront().replace("-","") + decryptData.get(EncryptParam.IDENTIFICATION_NUMBER), aesKey);

        String drivingLicenseNumber = dto.getIdentityType().equals(CertificationType.DRIVER) ?
                AESUtil.encryptWithCertificationServer(driverLocal + decryptData.get(EncryptParam.DRIVER_NUMBER), aesKey) :
                null;

        // 대표자 인증 처리
        if (!"0".equals(dto.getCeoSeqNo())) {
            JSONArray corpRegisterCeoInfo = getCorpRegisterCeoInfoFromScarpingServerTemp(user.corp().resUserIdentiyNo());

            try{
                List<String> listResCeoList = new ArrayList<>();

                // FIXME: (2025-02-05) 위펀 풀필먼트 (corp.idx() == 12911) 최근 대표자 변경 이슈로 임시로 1530 테이블 보도록 처리. 추후 다른 코드와 함께 롤백 되어야 함.
                if (corpRegisterCeoInfo == null || (envUtil.isProd() && user.corp().idx() == 12911)) {
                    // 스크래핑 서버에 정보 없을 경우 D1530으로 처리
                    Optional<D1530> d1530 = d1530Repo.findTopByIdxCorp(user.corp().idx());
                    if (d1530.isPresent()) {
                        if (!StringUtils.isEmpty(d1530.get().getD046())) {
                            listResCeoList.add(d1530.get().getD045());  // 직위
                            listResCeoList.add(d1530.get().getD046());  // 이름
                            listResCeoList.add(d1530.get().getD047());  // 주민번호
                            listResCeoList.add(d1530.get().getD048());  // 주소
                        }
                        if (!StringUtils.isEmpty(d1530.get().getD050())) {
                            listResCeoList.add(d1530.get().getD049());  // 직위
                            listResCeoList.add(d1530.get().getD050());  // 이름
                            listResCeoList.add(d1530.get().getD051());  // 주민번호
                            listResCeoList.add(d1530.get().getD052());  // 주소
                        }
                        if (!StringUtils.isEmpty(d1530.get().getD054())) {
                            listResCeoList.add(d1530.get().getD053());  // 직위
                            listResCeoList.add(d1530.get().getD054());  // 이름
                            listResCeoList.add(d1530.get().getD055());  // 주민번호
                            listResCeoList.add(d1530.get().getD056());  // 주소
                        }
                    }
                } else {
                    listResCeoList = FullTextJsonParserForScrape.parseCeoListInJsonFormatByCeoName(corpRegisterCeoInfo, dto.getKorName());
                }

                if(listResCeoList.size() == 0){
                    throw new GowidException(ResultCode.INVALID_CEO_EXCEPTION, "(99) " + ResultCode.INVALID_CEO_EXCEPTION.getDesc());
                }

                String fullAddress = listResCeoList.size() >= 4 ? listResCeoList.get(3) : ""; // 대표이사_주소1
                AddressResponseToSearch addressResponse = new AddressResponseToSearch();
                addressResponse = addressService.getAddressInfo(AddressInfo.getBasicAddress(fullAddress));
                String[] fullAddressList = fullAddress.split(", ");
                String[] resultAddress = {fullAddressList[0], String.join(", ", Arrays.copyOfRange(fullAddressList, 1, fullAddressList.length))};

                String zipNo = addressResponse.getResults().getJuso().get(0).getZipNo();

                CeoInfo ceoInfo = repoCeo.save(
                        CeoInfo.builder()
                                .cardIssuanceInfo(cardIssuanceInfo)
                                .ceoNumber(1)
                                .identificationNumber(identificationNumber)
                                .ceoAddressBasic(resultAddress[0])
                                .ceoAddressDetail(resultAddress[1])
                                .ceoZipCode(zipNo)
                                .ceoAddressDivisionCode(zipNo.length() == 5 ? "R" : "J")
                                .ceoAddressRoadNameCode(addressResponse.getResults().getJuso().get(0).getRnMgtSn())
                                .drivingLicenseNumber(drivingLicenseNumber)
                                .identificationIssuedDate(dto.getIssueDate())
                                .build()
                );
                if (cardApplicationForm != null) {
                    cardApplicationForm.ceoInfo(ceoInfo);
                }
            } catch (Exception e){
                log.error(e.getMessage());
                throw new GowidException(ResultCode.SHINHAN_1700_EXCEPTION, e.getMessage());
            }
        } else {
            // 카드관리자 인증 처리
            String nationality = "KR";
            String dateOfBirth = null;
            if (dto.getIdentityType().equals(CertificationType.FOREIGN)) {
                nationality = dto.getNation();
                dateOfBirth = convertToDate(dto.getIdentificationNumberFront());
            }


            CustomerIdentification customerIdentification = customerIdentificationRepository.save(
                    CustomerIdentification.builder()
                            .dateOfBirth(dateOfBirth)
                            .createdAt(LocalDateTime.now())
                            .build()
            );

            ManagerInfo managerInfo = repoManager.save(
                    ManagerInfo.builder()
                            .nation(nationality)
                            .identificationNumber(identificationNumber)
                            .drivingLicenseNumber(drivingLicenseNumber)
                            .certificationType(dto.getIdentityType())
                            .identificationIssuedDate(dto.getIssueDate())
                            .cardIssuanceInfo(cardIssuanceInfo)
                            .build()
            );

            cardApplicationForm.managerInfo(managerInfo);
            cardApplicationForm.customerIdentification(customerIdentification);
        }
    }

    @Transactional
    public void verifyCardPassword(HttpServletRequest request, CustomUser user, BcRegisterDto.CardPasswordReq cardPasswordReq) {

        String[] encryptParams;
        String registrationNumber = CommonUtil.replaceHyphen(user.corp().resCompanyIdentityNo());
        String companyNumber = user.corp().resCompanyNumber(); // 법인 전화번호

        CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormById(cardPasswordReq.getIdxCardApplicationForm());

        List<CardApplicationFormDetail> cardApplicationFormDetailList = cardApplicationFormDetailRepository.findByCardApplicationForm(cardApplicationForm);

        encryptParams = new String[]{EncryptParam.IDENTIFICATION_NUMBER, EncryptParam.DRIVER_NUMBER};

        Map<String, String> decryptData;
        try {
            decryptData = SecuKeypad.decrypt(request, cardPasswordReq.getEncryptData(), encryptParams);
        } catch (Exception e) {
            log.error("[KEYPAD] 키패드 에러 message : {}", e.getMessage(), e);
            throw new GowidException(ResultCode.SECURITY_KEYPAD_EXCEPTION, "서버 오류가 발생했습니다. 고객센터로 문의주세요.");
        }

        String passwordNumber = decryptData.get(EncryptParam.IDENTIFICATION_NUMBER);
        String passwordVerificationNumber = decryptData.get(EncryptParam.DRIVER_NUMBER);

        if (!passwordNumber.equals(passwordVerificationNumber)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "입력하신 비밀번호와 확인 비밀번호가 일치하지 않습니다.");
        }

        // 공용과 지정자 검증로직 분리
        if (cardApplicationForm.issuanceType() == ApplicationType.PUBLIC) {
            validatePublicCardPassword(passwordNumber, companyNumber, registrationNumber);
        } else {
            UserDetail userDetail = userDetailRepository.findByIdxUser(cardApplicationForm.user().idx());
            String identificationNumber = AESUtil.decryptWithCertificationServer(userDetail.getIdentificationNumber(), aesKey);
            String phoneNumber = cardApplicationForm.customerIdentification().mobilePhoneNumber();
            verifyDesignatedCardPassword(passwordNumber, identificationNumber, phoneNumber, companyNumber);
        }

        cardApplicationFormDetailList.forEach(item -> {
            item.cardPassword(AESUtil.encryptWithCertificationServer(passwordNumber, aesKey));
        });
    }

    private void verifyRepresentative(Long idxUser, CardIssuanceDto.IdentificationReq dto, Map<String, String> decryptData) {
        // 1700(신분증검증)
        DataPart1700 resultOfD1700 = issuanceService.process1700(idxUser, dto, decryptData);
        String code = resultOfD1700.getD008();
        String message = resultOfD1700.getD009();

        if (!Const.API_SHINHAN_RESULT_SUCCESS.equals(code)) {
            code = changeOldDriverLicenseErrorCode(code, message);
            throw new GowidException(ResultCode.SHINHAN_1700_EXCEPTION, "(" + code + ")" + message);
        }
    }

    private String changeOldDriverLicenseErrorCode(String code, String message) {
        String OLD_DRIVER_LICENSE_MSG = "예전 면허";
        if (message.contains(OLD_DRIVER_LICENSE_MSG)) {
            code = CeoVerifyCode.OLD_DRIVER_LICENSE_CODE.getCode();
        }
        return code;
    }

    private void verifyCorrespondRepresentativeByScrap(Corp corp, CardIssuanceDto.CeoValidReq dto) {
        //대표자 정보 가져오기
        String rawCorpRegistration = goweveCorporateRegisterScrapeService.getGoweveCorpRegisterByCorpRegNo(corp.resUserIdentiyNo());
        ScrapingResponse scrapingResponse;
        Optional<D1530> d1530Optional = null;
        D1530 d1530;

        // FIXME: (2025-02-05) 위펀 풀필먼트 (corp.idx() == 12911) 최근 대표자 변경 이슈로 임시로 1530 테이블 보도록 처리. 추후 다른 코드와 함께 롤백 되어야 함.
        if (StringUtils.isEmpty(rawCorpRegistration) || (envUtil.isProd() && corp.idx() == 12911)) {
            d1530Optional = d1530Repo.findTopByIdxCorp(corp.idx());

            if (d1530Optional != null && d1530Optional.isPresent()) {
                d1530 = d1530Optional.get();
            } else {
                throw new GowidException(ResultCode.EXCEPTION, "스크래핑 오류 발생");
            }
        } else {
            try {
                scrapingResponse = ScrapingResultUtils.getNewApiResult(rawCorpRegistration);
            } catch (Exception e) {
                throw new GowidException(ResultCode.EXCEPTION, "스크래핑 오류 발생");
            }
            d1530 = fullTextService.build1530(corp, scrapingResponse.getScrapingResponse()[1]);
        }

        boolean isValidCeoInfo = isValidCeoInfo(d1530 , dto);
        if (isValidCeoInfo) {
            throw new GowidException(ResultCode.BAD_REQUEST, "대표자 정보가 일치하지 않습니다.");
        }

    }

    private void verifyCorrespondRepresentative(Long idxCorp, CardIssuanceDto.CeoValidReq dto) {
        D1530 d1530 = fullTextService.findFirstByIdxCorpIn1530OrThrow(idxCorp);

        boolean isValidCeoInfo = isValidCeoInfo(d1530, dto);
        if (isValidCeoInfo) {
            throw new GowidException(ResultCode.BAD_REQUEST, "대표자 정보가 일치하지 않습니다.");
        }
    }

    private boolean isValidCeoInfo(D1530 d1530, CardIssuanceDto.CeoValidReq dto) {
        return !checkCeo(d1530.getD046(), d1530.getD047(), dto)
                && !checkCeo(d1530.getD050(), d1530.getD051(), dto)
                && !checkCeo(d1530.getD054(), d1530.getD055(), dto);
    }

    private boolean checkCeo(String korName, String idNum, CardIssuanceDto.CeoValidReq dto) {
        if (!StringUtils.hasText(idNum)) {
            return false;
        }

        if (dto.getName().equals(korName)) {
            if (!"KR".equals(dto.getNation())) {
                return true;
            } else {
                idNum = Seed128.decryptEcb(idNum);
                return dto.getIdentificationNumberFront().substring(0, 6).equals(idNum.substring(0, 6));
            }
        }

        return false;
    }

    public String findShinhanDriverLocalCode(String code) {
        return repoCodeDetail.findFirstByValue1OrValue2AndCode(code, code, CommonCodeType.SHINHAN_DRIVER_LOCAL_CODE).orElseThrow(
                () -> EntityNotFoundException.builder()
                        .entity("CommonCodeDetail")
                        .build()
        ).code1();
    }

    /**
     * 발급단계 저장
     *
     * @param depthKey 발급단계 값
     */
    @Transactional(rollbackFor = Exception.class)
    public IssuanceDepthResponseDto saveIssuanceDepth(Long idxUser, IssuanceDepth depthKey, CardType cardType) {
        User user = findUser(idxUser);
        CardIssuanceInfo cardIssuanceInfo = cardIssuanceInfoService.findByUserAndCardType(user, cardType);
        String prevDepth = cardIssuanceInfo.issuanceDepth().name();
        cardIssuanceInfo.updateIssuanceDepth(depthKey);

        log.info("succeed to update issuance depth : "
                + prevDepth + " -> ", depthKey.name());

        return IssuanceDepthResponseDto.from(cardIssuanceInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public FinancialConsumersResponseDto updateFinancialConsumersInfo(Long idxUser, CardType cardType, FinancialConsumersRequestDto dto) {
        User user = findUser(idxUser);
        return financialConsumersService.updateOverFiveEmployees(user, cardType, dto.getOverFiveEmployees());
    }



    private User findUser(Long idxUser) {
        return repoUser.findById(idxUser).orElseThrow(
                () -> new NotFoundException(GowidMessageGroup.G00009)
        );
    }

    private JSONArray getCorpRegisterCeoInfoFromScarpingServer(String resUserIdentifyNo) {
        final String rawCorpRegistration = goweveCorporateRegisterScrapeService.getGoweveCorpRegisterBy(resUserIdentifyNo);
        ScrapingResponse corporateRegisterScrapingResponse;

        try {
            corporateRegisterScrapingResponse = ScrapingResultUtils.getNewApiResult(rawCorpRegistration);
        } catch (Exception e) {
            throw new GowidException(ResultCode.EXCEPTION);
        }

        JSONObject corporateRegisterJsonData = corporateRegisterScrapingResponse.getScrapingResponse()[1];

        JSONArray jsonArrayResCEOList = (JSONArray) corporateRegisterJsonData.get("ceoDtoList");

        return jsonArrayResCEOList;
    }

    private JSONArray getCorpRegisterCeoInfoFromScarpingServerTemp(String resUserIdentifyNo) {
        final String rawCorpRegistration = goweveCorporateRegisterScrapeService.getGoweveCorpRegisterByCorpRegNo(resUserIdentifyNo);
        ScrapingResponse corporateRegisterScrapingResponse;

        if (!StringUtils.isEmpty(rawCorpRegistration)) {
            return null;
        }

        try {
            corporateRegisterScrapingResponse = ScrapingResultUtils.getNewApiResult(rawCorpRegistration);
        } catch (Exception e) {
            return null;
        }

        JSONObject corporateRegisterJsonData = corporateRegisterScrapingResponse.getScrapingResponse()[1];

        JSONArray jsonArrayResCEOList = (JSONArray) corporateRegisterJsonData.get("ceoDtoList");

        return jsonArrayResCEOList;
    }

    private boolean verifyDesignatedCardPassword(String password, String identificationNumber, String phoneNumber, String companyNumber) {
        if (!isValidLength(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 4자리 숫자여야 합니다.");
        }

        if (isAllSameDigits(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 동일한 숫자 4자리로 설정할 수 없습니다.");
        }

        if (isSequential(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 연속되는 숫자로 설정할 수 없습니다.");
        }

        if (identificationNumber != null && identificationNumber.length() > 6 && identificationNumber.substring(0, 6).contains(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 주민등록번호 앞자리의 생년월일로 설정할 수 없습니다.");
        }

        if (identificationNumber != null && identificationNumber.length() > 6 && identificationNumber.substring(6).contains(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 주민등록번호 뒷자리 숫자로 설정할 수 없습니다.");
        }

        if (phoneNumber != null && phoneNumber.split("-")[1].equals(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 휴대폰 번호 앞자리 숫자를 포함하여 설정할 수 없습니다.");
        }

        if (phoneNumber != null && phoneNumber.split("-")[2].equals(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 휴대폰 번호 뒷자리 숫자를 포함하여 설정할 수 없습니다.");
        }

        if (isPhoneNumber(password, companyNumber)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 법인 전화번호와 동일한 숫자 4자리로 설정할 수 없습니다.");
        }

        return true;
    }

    private boolean validatePublicCardPassword(String password, String phoneNumber, String registrationNumber) {
        if (!isValidLength(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 4자리 숫자여야 합니다.");
        }

        if (isAllSameDigits(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 동일한 숫자 4자리로 설정할 수 없습니다.");
        }

        if (isSequential(password)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 연속되는 숫자로 설정할 수 없습니다.");
        }

        if (isPhoneNumber(password, phoneNumber)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 법인 전화번호와 동일한 숫자 4자리로 설정할 수 없습니다.");
        }

        if (isContainedInRegistrationNumber(password, registrationNumber)) {
            throw new GowidException(ResultCode.BAD_REQUEST, "비밀번호는 사업자 번호 내 연속 숫자 4자리로 설정할 수 없습니다.");
        }

        return true;
    }

    // 1. 비밀번호 길이 확인 (4자리)
    private static boolean isValidLength(String password) {
        return password != null && password.length() == 4 && password.matches("\\d{4}");
    }

    // 2. 동일 숫자 여부 확인
    private static boolean isAllSameDigits(String password) {
        char firstChar = password.charAt(0);
        return password.chars().allMatch(c -> c == firstChar);
    }

    // 3. 연속 오름/내림 숫자 여부 확인
    private static boolean isSequential(String password) {
        for (int i = 0; i < password.length() - 1; i++) {
            int diff = password.charAt(i + 1) - password.charAt(i);
            if (diff != 1 && diff != -1) {
                return false;
            }
        }
        return true; // 모든 숫자가 연속적이면 true
    }

    // 3. 휴대폰 번호 포함 여부 확인
    private static boolean isPhoneNumber(String password, String phoneNumber) {
        String[] split = phoneNumber.split("-");
        if (password.equals(split[1]) || password.equals(split[2])) {
            return true;
        }
        return false; // 모든 숫자가 연속적이면 true
    }


    // 5. 사업자등록번호 중간 4자리 비교
    private static boolean isContainedInRegistrationNumber(String password, String serialNumber) {
        if (serialNumber.contains(password)) {
            return true;
        } else {
            return false;
        }
    }

    public String convertToDate(String input) {
        // 주민등록번호 앞자리와 성별 번호 분리
        String birth = input.split("-")[0]; // "990118"
        int genderCode = Integer.parseInt(input.split("-")[1]); // "1"

        // 연도, 월, 일 추출
        int year = Integer.parseInt(birth.substring(0, 2));
        int month = Integer.parseInt(birth.substring(2, 4));
        int day = Integer.parseInt(birth.substring(4, 6));

        // 성별 번호로 연도 계산
        int fullYear = (genderCode == 1 || genderCode == 2) ? 1900 + year : 2000 + year;

        // 날짜 생성 및 포맷팅
        LocalDate date = LocalDate.of(fullYear, month, day);
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
