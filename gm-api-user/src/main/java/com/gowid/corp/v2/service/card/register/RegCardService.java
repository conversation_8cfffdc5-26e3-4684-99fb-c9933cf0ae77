package com.gowid.corp.v2.service.card.register;

import com.gowid.corp.core.domain.bc.*;
import com.gowid.corp.core.domain.bc.type.*;
import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.dto.BcCardInfoDto;
import com.gowid.corp.core.dto.response.BcCardInfoResDto;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.repository.bc.CardApplicationFormDetailRepository;
import com.gowid.corp.core.repository.bc.CardApplicationFormRepository;
import com.gowid.corp.core.repository.cardIssuanceInfo.CardIssuanceInfoRepository;
import com.gowid.corp.core.repository.corp.CorpRepository;
import com.gowid.corp.core.domain.user.Authority;
import com.gowid.corp.core.domain.user.Role;
import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.exception.BadRequestException;
import com.gowid.corp.core.exception.NotFoundException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import com.gowid.corp.core.repository.querydsl.CustomCardApplicationFormDetailRepository;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.CardIssuanceDto;
import com.gowid.corp.v2.dto.card.RegisterDto;
import com.gowid.corp.v2.factory.CardProcessFactory;
import com.gowid.corp.v2.service.card.BcCardService;
import com.gowid.corp.v2.service.card.CardProcessService;
import com.gowid.corp.v2.service.card.LotteCardServiceV2;
import com.gowid.corp.v2.service.card.ShinhanCardServiceV2;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class RegCardService {
    private final ShinhanCardServiceV2 shinhanCardService;
    private final LotteCardServiceV2 lotteCardService;
    private final RegCommonService regCommonService;
    private final CardApplicationFormRepository cardApplicationFormRepository;
    private final CardApplicationFormDetailRepository cardApplicationFormDetailRepository;
    private final CustomCardApplicationFormDetailRepository customCardApplicationFormDetailRepository;
    private final BcCardService bcCardService;

    private final CardIssuanceInfoRepository repoCardIssuanceInfo;
    private final CorpRepository repoCorp;
    private final CardProcessFactory cardProcessFactory;

    @Transactional(rollbackFor = Exception.class)
    public RegisterDto.Status saveCardInfo(CustomUser customUser, RegisterDto.CardInfo dto) {
        if(dto.getCardType().equals(CardType.GOWID_LOTTE)){
            if(dto.getGreenCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "롯데카드는 카드별 99장 이상은 발급 불가 합니다.");
            }
            if(dto.getBlackCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "롯데카드는 카드별 99장 이상은 발급 불가 합니다.");
            }
            if(dto.getGreenTrafficCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "롯데카드는 카드별 99장 이상은 발급 불가 합니다.");
            }
            if(dto.getBlackTrafficCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "롯데카드는 카드별 99장 이상은 발급 불가 합니다.");
            }
            if(dto.getHiPassCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "롯데카드는 카드별 99장 이상은 발급 불가 합니다.");
            }
        } else if(dto.getCardType().equals(CardType.GOWID_SHINHAN)) {
            if(dto.getRequestCount() < 1L || dto.getRequestCount() > 40L){
                throw new GowidException(ResultCode.BAD_REQUEST, "신한카드는 최대 40장 까지 신청 가능 합니다.");
            }
        } else if(dto.getCardType().equals(CardType.GOWID_BC)){
            if(dto.getRequestCount() < 1L || dto.getRequestCount() > 99L){
                throw new GowidException(ResultCode.BAD_REQUEST, "BC카드는 최대 99장 까지 신청 가능 합니다.");
            }
        }

        CardIssuanceInfo cardInfo = repoCardIssuanceInfo.findByCorpAndCardType(customUser.corp(), dto.getCardType() != null ? dto.getCardType() : CardType.GOWID).orElseThrow(
                () -> new NotFoundException(GowidMessageGroup.G00005)
        );

        CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(customUser.corp(), customUser.user(), ApplicationType.PUBLIC);

        // 카드정보 저장 및 수정
        log.info("saveCardInfo - ");
        procCardInfo(customUser, dto, cardInfo, cardApplicationForm);

        return RegisterDto.Status.builder().boolStatus(true).build();
    }

    private void procCardInfo(CustomUser customUser, RegisterDto.CardInfo dto, CardIssuanceInfo cardInfo, CardApplicationForm cardApplicationForm) {
        validateCardInfo(dto);

        CardIssuanceDto.RegisterCard regCard = CardIssuanceDto.RegisterCard.builder()
                .requestCount(dto.getRequestCount())
                .greenCount(dto.getGreenCount())
                .blackCount(dto.getBlackCount())
                .greenTrafficCount(dto.getGreenTrafficCount())
                .blackTrafficCount(dto.getBlackTrafficCount())
                .hiPassCount(dto.getHiPassCount())
                .addressBasic(cardInfo.card().addressBasic())
                .addressDetail(cardInfo.card().addressDetail())
                .zipCode(cardInfo.card().zipCode())
                .addressKey(cardInfo.card().addressKey())
                .receiveType(cardInfo.card().receiveType())
                .isDynamicCurrencyConversion(cardInfo.card().isDynamicCurrencyConversion())
                .build();

        if (cardInfo.cardCompany().equals(CardCompany.BC)) {
            cardInfo.card().paymentDay(23);
        }

        // TODO: [1:N] BC 추가 필요
        if (CardCompany.isShinhan(cardInfo.cardCompany())) {
            shinhanCardService.updateShinhanFulltextCard(cardInfo, cardInfo.card().grantLimit(), regCard);
            shinhanCardService.setCardInfoCard(cardInfo, regCard, cardInfo.card().calculatedLimit(), cardInfo.card().grantLimit());
        } else if (CardCompany.isLotte(cardInfo.cardCompany())) {
            lotteCardService.updateD1100Card(customUser.user(), cardInfo.card().grantLimit(), cardInfo.card().calculatedLimit(), cardInfo.card().hopeLimit(), regCard);
            lotteCardService.setCardInfoCard(cardInfo, regCard, cardInfo.card().calculatedLimit(), cardInfo.card().grantLimit());
        } else if (CardCompany.isBC(cardInfo.cardCompany())) {
            // 기존 데이터 삭제
            List<CardApplicationFormDetail> cardApplicationFormList = cardApplicationFormDetailRepository.findByCardApplicationForm(cardApplicationForm);
            cardApplicationFormDetailRepository.deleteAll(cardApplicationFormList);

            Long order = 1L;
            for (BcCardInfoDto bcCardInfoDto : dto.getBcCardInfoList()) {
                order = saveBcCardInfoList(bcCardInfoDto, cardApplicationForm, order);
            }
        }
    }

    // 카드 상품 종류마다 개수가 다르기 때문에 개수 만큼 CardApplicationFormDetail을 save
    private Long saveBcCardInfoList(BcCardInfoDto item,
                                    CardApplicationForm cardApplicationForm,
                                    Long order) {
        List<CardApplicationFormDetail> cardApplicationFormDetailList = new ArrayList<>();

        for (int i = 0; i < item.getCount(); i++) {
            cardApplicationFormDetailList.add(
                    CardApplicationFormDetail.builder()
                            .cardDesignDivisionCode(item.getCardColor())
                            .cardBrandCode(CardBrandCodeType.AMEX)
                            .cardProductCode("103867")
                            .cardGrade(CardGradeType.GOLD)
                            .deferredTrafficCardDivisionCode(item.getIsDeferredTrafficCard() ? "1" : "null")
                            .cardApplicationForm(cardApplicationForm)
                            .cardLimit(cardApplicationForm != null && cardApplicationForm.corporateCompany() != null ? cardApplicationForm.corporateCompany().corporateCompanyLimit() : null)
                            .corporateCompanyNumber(cardApplicationForm != null && cardApplicationForm.corporateCompany() != null ? cardApplicationForm.corporateCompany().corporateCompanyNumber() : null)
                            .corp(cardApplicationForm.corp())
                            .registrationNumber(cardApplicationForm.corp().resCompanyIdentityNo())
                            .cardHolderType(ApplicationCardDivisionType.COMPANY_SHARED)
                            .issuanceStatus(IssuanceStatusType.CREATED)
                            .issuanceSeq(order++)
                            .createdAt(LocalDateTime.now())
                            .updatedAt(LocalDateTime.now())
                            .build()
            );
        }

        cardApplicationFormDetailRepository.saveAll(cardApplicationFormDetailList);
        return order;
    }

    public RegisterDto.CardInfo getCardInfo(CustomUser customUser, CardType cardType) {
        List<BcCardInfoResDto> bcCardInfoDtoList = null;
        List<BcCardInfoDto> bcCardInfoDtos = null;
        CardIssuanceInfo cardInfo = repoCardIssuanceInfo.findByCorpAndCardType(customUser.corp(), cardType != null ? cardType : CardType.GOWID).orElseThrow(
                () -> new NotFoundException(GowidMessageGroup.G00005)
        );

        if (CardCompany.isBC(cardInfo.cardCompany())) {
            CardApplicationForm cardApplicationForm = bcCardService.getCardApplicationFormByIssuanceType(customUser.corp(), customUser.user(), ApplicationType.PUBLIC);

            bcCardInfoDtoList =
                    customCardApplicationFormDetailRepository.getBcCardColorTypeAndDeferredTrafficCardDivisionCode(cardApplicationForm);

            bcCardInfoDtos = bcCardInfoDtoList.stream().map(item ->
                    BcCardInfoDto.builder()
                            .cardColor(item.getCardColor())
                            .count(item.getCount())
                            .isDeferredTrafficCard(item.getDeferredTrafficCard().equals("1") ? true : false)
                            .build()
            ).collect(Collectors.toList());
        }

        return RegisterDto.CardInfo.builder()
                .requestCount(cardInfo.card().requestCount())
                .greenCount(cardInfo.card().greenCount())
                .blackCount(cardInfo.card().blackCount())
                .greenTrafficCount(cardInfo.card().greenTrafficCount())
                .blackTrafficCount(cardInfo.card().blackTrafficCount())
                .hiPassCount(cardInfo.card().lotteHiPassCount())
                .bcCardInfoList(bcCardInfoDtos)
                .build();
    }

    private void validateCardInfo(RegisterDto.CardInfo dto) {
        if (ObjectUtils.isEmpty(dto.getGreenCount()) && ObjectUtils.isEmpty(dto.getBlackCount()) &&
                ObjectUtils.isEmpty(dto.getGreenTrafficCount()) && ObjectUtils.isEmpty(dto.getBlackTrafficCount()) &&
                CollectionUtils.isEmpty(dto.getBcCardInfoList())) {
            throw new BadRequestException(GowidMessageGroup.G02007);
        }
    }

    @Transactional
    public RegisterDto.Info getInfo(CustomUser user) {
        try {
            User findMasterUser = repoCorp.findByIdx(user.corp().idx()).user();

            if(Authority.from(findMasterUser.authorities()).equals(Role.ROLE_MASTER)) {
                return RegisterDto.Info.builder()
                        .cardDepthInfo(
                                repoCardIssuanceInfo.findAllByUser(findMasterUser).isPresent() ?
                                        repoCardIssuanceInfo.findAllByUser(findMasterUser).get()
                                                .stream()
                                                .map(RegisterDto.CardDepthInfo::from).collect(Collectors.toList()) : null
                        )
                        .build();
            }
        } catch (NullPointerException npe) {
            log.error("Corp is null(idxUser: {}).", user.idx());
        }

        return null;
    }

    public CardIssuanceDto.FinancialConsumerProtectionActResDto addFinancialConsumerProtectionAct(final Long idx,
                                                                                                  final CardIssuanceDto.FinancialConsumerProtectionActReqDto request) {
        final CardProcessService cardProcessor = cardProcessFactory.getCardProcessor(request.getCardCompany());
        return cardProcessor.addFinancialConsumerProtectionAct(idx, request);
    }

    public CardIssuanceDto.FinancialConsumerProtectionActResDto updateFinancialConsumerProtectionAct(final Long idx,
                                                                                                     final CardIssuanceDto.FinancialConsumerProtectionActReqDto request) {
        final CardProcessService cardProcessor = cardProcessFactory.getCardProcessor(request.getCardCompany());
        return cardProcessor.updateFinancialConsumerProtectionAct(idx, request);
    }
}
