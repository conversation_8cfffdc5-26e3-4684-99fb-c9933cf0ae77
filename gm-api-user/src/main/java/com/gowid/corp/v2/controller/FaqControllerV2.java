package com.gowid.corp.v2.controller;

import com.gowid.corp.core.annotation.CurrentUser;
import com.gowid.corp.core.security.CustomUser;
import com.gowid.corp.dto.gateway.ApiResponse;
import com.gowid.corp.v2.dto.faq.FaqDto;
import com.gowid.corp.v2.service.faq.FaqService2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
public class FaqControllerV2 extends FaqBaseController {

	public static class URI {
		public static final String FAQ_SEARCH = "/search";
	}

	private final FaqService2 faqService;

	@Deprecated	// 화면에서도 사용되지 않는다면 제거 필요
	@PostMapping(value = URI.FAQ_SEARCH)
	@Operation(summary = "FAQ 검색어 저장")
	public ApiResponse<?> updateCheckListData(@Parameter(hidden = true) @CurrentUser CustomUser user,
											  @RequestBody FaqDto.FaqSearchHistoryReq req) {
		faqService.saveFaqSearchHistory(user, req);
		return ApiResponse.OK();
	}
}
