package com.gowid.corp.v2.dto;

import com.gowid.corp.core.domain.user.User;
import com.gowid.corp.core.domain.user.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStatusResDto {

    private Long userId;

    private UserStatus userStatus;

    public static UserStatusResDto of(User user) {
        return UserStatusResDto.builder()
            .userId(user.idx())
            .userStatus(user.status())
            .build();
    }
}
