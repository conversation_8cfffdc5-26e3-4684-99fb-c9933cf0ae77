package com.gowid.corp.v2.service.card;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gowid.corp.core.dto.GowidResponseV2;
import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.dto.feign.ScrapeBusinessRegisterReqDto;
import com.gowid.corp.exception.ScrapingNotFoundDataException;
import com.gowid.corp.feign.GoweveScrapeFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoweveBusinessRegisterScrapeService {

    private final GoweveScrapeFeignClient scrapingFeignClient;
    private final ObjectMapper objectMapper;

    @Value("${feign.service.scrape.government-api-key}")
    private String governmentApiKey;

    @Retryable(value = ScrapingNotFoundDataException.class, backoff = @Backoff(delay = 10000), maxAttempts = 4)
    public String scrapeBusinessRegister(final String registrationNumber) {
        String result;
        try {
            result = scrapingFeignClient.scrapeBusinessRegister(governmentApiKey, true, new ScrapeBusinessRegisterReqDto(registrationNumber));

        } catch (GowidException e) {
            if (ResultCode.EXCEPTION.getCode() == e.getResultCode().getCode()) {
                throw new GowidException(ResultCode.BAD_REQUEST, "사업자 등록증 스크래핑에 문제가 발생했습니다." + e.getMessage());
            } else if (ResultCode.SCRAPE_GOVERNMENT_HOME_TAX_UNREGISTER_EXCEPTION.getCode() == e.getResultCode().getCode()) {
                throw new GowidException(ResultCode.BAD_REQUEST, "홈택스에 등록되지 않은 인증서입니다.\n" +
                        "홈택스에 연동된 인증서 등록 후 다시 시도 부탁드립니다.\n" +
                        "동일한 문제가 반복되면 고객센터로 연락해주세요.");
            } else if (ResultCode.SCRAPE_GOVERNMENT_HOME_TAX_DISCARD_EXCEPTION.getCode() == e.getResultCode().getCode()) {
                throw new GowidException(ResultCode.BAD_REQUEST, "등록할 수 없는 만료(폐기)된 인증서입니다.\n" +
                        "유효한 인증서로 다시 시도해 주세요.");
            }
            throw e;
        } catch (Exception e) {
            throw new GowidException(ResultCode.BAD_REQUEST, "사업자 등록증 스크래핑에 문제가 발생했습니다." + e.getMessage());
        }
        JSONObject jsonObject;
        try {
            jsonObject = (JSONObject) new JSONParser().parse(result);
        } catch (Exception e) {
            log.error("json parsing error : {}, stackTrace : {}", e.getMessage(), e);
            throw new GowidException(ResultCode.BAD_REQUEST, "json을 파싱하는데 문제가 발생했습니다.");
        }

        final JSONObject resultCode = (JSONObject) jsonObject.get("result");
        if (ResultCode.SUCCESS.getCode() != (Long) resultCode.get("code")) {
            throw new GowidException(ResultCode.BAD_REQUEST, resultCode.get("desc").toString());
        }

        if (jsonObject.get("data") == null && ResultCode.SUCCESS.getCode() == (int) resultCode.get("code")) {
            throw new ScrapingNotFoundDataException(ResultCode.NOT_FOUND_SCRAPE_DATA.getDesc());
        }

        return result;
    }

    public String scrapeBusinessRegisterForInternal(final String registrationNumber) {
        final String result;
        try {
            result = scrapingFeignClient.scrapeBusinessRegister(governmentApiKey, true, new ScrapeBusinessRegisterReqDto(registrationNumber));
        } catch (GowidException e) {
            if (ResultCode.EXCEPTION.getCode() == e.getResultCode().getCode()) {
                throw new GowidException(ResultCode.BAD_REQUEST, "사업자 등록증 스크래핑에 문제가 발생했습니다." + e.getMessage());
            }
            throw e;
        } catch (Exception e) {
            throw new GowidException(ResultCode.BAD_REQUEST, "사업자 등록증 스크래핑에 문제가 발생했습니다." + e.getMessage());
        }

        final GowidResponseV2<Object> gowidResponseV2 = readJson(result);
        if (ResultCode.SUCCESS.getCode() != gowidResponseV2.getResult().code) {
            throw new GowidException(ResultCode.BAD_REQUEST, gowidResponseV2.getResult().desc);
        }
        if (gowidResponseV2.getData() == null) {
            throw new ScrapingNotFoundDataException(ResultCode.NOT_FOUND_SCRAPE_DATA.getDesc());
        }

        return result;
    }

    private GowidResponseV2<Object> readJson(final String result) {
        try {
            return objectMapper.readValue(result, GowidResponseV2.class);
        } catch (JsonProcessingException e) {
            log.error("json parsing error : {}", e.getMessage(), e);
            throw new GowidException(ResultCode.BAD_REQUEST, "json을 파싱하는데 문제가 발생했습니다.");
        }
    }

    public String getBusinessRegister(final String registrationNumber) {
        String result = scrapingFeignClient.getBusinessRegister(governmentApiKey, registrationNumber);
        JSONObject jsonObject;
        try {

            jsonObject = (JSONObject) new JSONParser().parse(result);
        } catch (Exception e) {
            log.error("json parsing error : {}, stackTrace : {}", e.getMessage(), e);
            throw new GowidException(ResultCode.BAD_REQUEST, "스크래핑 파싱하는데 문제가 발생했습니다.");
        }

        final JSONObject resultCode = (JSONObject) jsonObject.get("result");
        if (ResultCode.SUCCESS.getCode() != (Long) resultCode.get("code")) {
            throw new GowidException(ResultCode.BAD_REQUEST, resultCode.get("desc").toString());
        }

        return result;
    }
}
