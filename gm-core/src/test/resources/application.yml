spring:
  profiles:
    group:
      test: jwt, core-stage, redis-stage, resx

#    active: junit, local, mobile
#    include:
#      - swagger
#      - jwt
#      - core
#      - resx
#      - secukeypad
  devtools:
    livereload:
      enabled: true
  freemarker:
    cache: false
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB

server:
  port: 8080
  error:
    include-stacktrace: never

logging:
  level:
    root: info
    com.gowid.corp: debug
    org.hibernate.SQL: warn
    org.hibernate.type.descriptor.sql.BasicBinder: warn
    org.springframework.jdbc.core.JdbcTemplate: warn
    org.springframework.jdbc.core.StatementCreatorUtils: trace
    com.zaxxer.hikari: debug

---

spring:
  config:
    activate:
      on-profile: test
  mail:
    sender: gowid <<EMAIL>>
    riskteam: gowid <<EMAIL>>
    host: email-smtp.us-west-2.amazonaws.com
    port: 587
    username: AKIAXD7XVDFHCXKO7DE5
    password: BLkZ5wIHk5WRB5qNhZoLjVKDqPA4za0TR+8WXW1vWXc6
    protocol: smtp
    properties:
      mail:
        debug: true
        smtp:
          auth: true
          starttls.enable: true
    default-encoding: UTF-8

tomcat:
  ajp:
    protocol: AJP/1.3
    port: 8009
    enabled: true
    connection-timeout: 60000

gateway:
  idc:
    shinhan: http://************:8080
    lotte: http://************:8090
#  idc:
#    host: ************:8080
#    protocol: http

encryption:
  keypad:
    enable: true
  seed128:
    enable: true

stockholder:
  file:
    size: 2097152
    type: jpg, jpeg, tif, tiff

mail:
  approved:
    send-enable: false
  receipt:
    send-enable: false

logging:
  level:
    org.hibernate.SQL: debug
    org.hibernate.type: trace
    org.hibernate.type.descriptor.sql.BasicBinder: debug

sentry:
  dsn: https://<EMAIL>/5394758
  enabled: true
---
