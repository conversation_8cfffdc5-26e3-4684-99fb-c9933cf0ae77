spring:
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      use-new-id-generator-mappings: true
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      max-lifetime: 540000

---
spring:
  profiles: test
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true
    show-sql: true
    generate-ddl: true
    open-in-view: false
  datasource:
    initialization-mode: always
    url: **************************************************************************************
    username: gowidapi
    password: La@R_BV2

---
