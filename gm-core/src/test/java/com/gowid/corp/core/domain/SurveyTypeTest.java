package com.gowid.corp.core.domain;

import com.gowid.corp.core.domain.etc.SurveyType;
import com.gowid.corp.core.exception.GowidException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class SurveyTypeTest {

    @Test
    @DisplayName("카드 발급 시 입력된 설문조사 제목이 NULL이면 IllegalArgumentException를 발생시킨다")
    void Should_IllegalArgumentException_When_surveyTitleIsNull() {
        Assertions.assertThrows(IllegalArgumentException.class,
                () -> SurveyType.hasSurveyTitle(null)
        );
    }

    @ValueSource(strings = {"wrong type", "", " "})
    @ParameterizedTest(name = "카드 발급 시 입력된 설문조사 제목 타입이 'SURVEY_FUNNELS' 아닌 경우 GowidException를 발생시킨다")
    void Should_gowidException_When_surveyTitleIsNotSurveyFunnels(final String wrongSurveyTitle) {
        Assertions.assertThrows(GowidException.class,
                () -> SurveyType.hasSurveyTitle(wrongSurveyTitle)
        );
    }

    @Test
    @DisplayName("카드 발급 시 입력된 설문조사 제목 타입이 'SURVEY_FUNNELS'인 경우 true를 응답한다.")
    void Should_true_When_surveyTitleIsSurveyFunnels() {
        // given
        final String surveyType = SurveyType.SURVEY_FUNNELS.name();

        // when
        boolean result = SurveyType.hasSurveyTitle(surveyType);

        // then
        Assertions.assertTrue(result);
    }
}
