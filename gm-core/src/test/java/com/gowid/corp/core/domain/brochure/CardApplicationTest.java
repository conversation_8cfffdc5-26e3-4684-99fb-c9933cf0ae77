package com.gowid.corp.core.domain.brochure;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class CardApplicationTest {

    @Test
    @DisplayName("적합상태 메일을 발송한 도입신청서라면 발급 가능")
    void Should_canSignUp() {
        final CardApplication cardApplication = CardApplication.builder()
                .isSendMail(true)
                .reviewStatus(JudgeType.SUITABLE)
                .build();

        Assertions.assertThat(cardApplication.cannotSignUp()).isFalse();
    }

}
