package com.gowid.corp.core;

import org.assertj.core.api.Assertions;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class JasyptTest {

    @ParameterizedTest
    @ValueSource(strings = {
        "TEST"
    })
    @DisplayName("Jasypt 암호화 후 복호화한 값이 최초값과 동일해야 성공한다.")
    void Should_ReturnTrue_If_DecryptedStringEqualsToTargetString(String target) {
        StandardPBEStringEncryptor pbeEnc = new StandardPBEStringEncryptor();
        pbeEnc.setPassword("Gowid@CaRdVS%^EveR");
        pbeEnc.setAlgorithm("PBEWithMD5AndDES");
        String encrypted = pbeEnc.encrypt(target);
        System.out.println("encrypted : " + encrypted);
        String decrypted = pbeEnc.decrypt(encrypted);
        System.out.println("decrypted : " + decrypted);

        Assertions.assertThat(decrypted).isEqualTo(target);
    }
}
