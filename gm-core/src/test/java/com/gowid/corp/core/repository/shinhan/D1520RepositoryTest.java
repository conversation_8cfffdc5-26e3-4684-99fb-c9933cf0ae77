package com.gowid.corp.core.repository.shinhan;

import com.gowid.corp.core.abstracts.AbstractJpaTest;
import com.gowid.corp.core.domain.shinhan.D1520;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class D1520RepositoryTest extends AbstractJpaTest {

	@Autowired
	private D1520Repository d1520Repository;

	@Test
	@Transactional
	public void should_return_2rows_when_findByIdxCorpGroupByD015(){
		long idxCorp = 250;

		List<D1520> d1520 = d1520Repository.findByIdxCorpGroupByYear(idxCorp);

		assertThat(d1520).hasSize(2);
		assertThat(d1520.get(0).getD015()).isEqualTo("2018");
		assertThat(d1520.get(1).getD015()).isEqualTo("2019");
	}
}
