package com.gowid.corp.core.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class NumberUtilsTests {

	@Test
	public void addComma(){
		Long longValue = 20000000000L;
		Integer integerValue = 1200000000;
		Double doubleValue = 300000D;
		Double doubleDecimalPointValue = 400000.123;

		String convertWithLong = NumberUtils.addComma(longValue);
		String convertWithInteger = NumberUtils.addComma(integerValue);
		String convertWithDouble = NumberUtils.addComma(doubleValue);
		String convertWithDoubleDecimalPoint = NumberUtils.addComma(doubleDecimalPointValue);

		assertThat(convertWithLong).isEqualTo("20,000,000,000");
		assertThat(convertWithInteger).isEqualTo("1,200,000,000");
		assertThat(convertWithDouble).isEqualTo("300,000");
		assertThat(convertWithDoubleDecimalPoint).isEqualTo("400,000.123");
	}

	@Test
	@DisplayName("기본형 double 타입을 string으로 변환했을 때 값이 변하지 않아야 한다.")
	void Should_return_same_values_When_double_convert_to_string() {
		// given
		double value = 20000000;

		// when
		String doubleString = NumberUtils.doubleToString(value);

		// then
		Assertions.assertAll(() -> {
			Assertions.assertEquals("20000000", doubleString);
			Assertions.assertNotEquals("2.0E7", doubleString);
		});
	}

	@Test
	@DisplayName("기본형 double 타입을 string으로 변환했을 때 값이 변하지 않아야 한다.")
	void Should_returnSameValues_When_doubleConvertToString() {
		// given
		double value = 9999999999999.22d;

		// when
		String actual = NumberUtils.doubleToString(value);

		// then
		String expected = "9999999999999.22";
		Assertions.assertEquals(expected, actual);
	}

	@Test
	void stringToLong() {
		String value1 = "0";
		String value2 = "20000";
		String value3 = "text";

		Long convertedValue1 = NumberUtils.stringToLong(value1);
		Long convertedValue2 = NumberUtils.stringToLong(value2);
		Long convertedValue3 = NumberUtils.stringToLong(value3);

		assertThat(convertedValue1).isEqualTo(0L);
		assertThat(convertedValue2).isEqualTo(20000L);
		assertThat(convertedValue3).isEqualTo(0L);
	}

	@Test
	@DisplayName("target이 comparison 미만일 때 true 리턴한다")
	void Should_return_true_When_target_value_is_under_comparison() {
		// given
		BigDecimal target = new BigDecimal(10000000);
		BigDecimal comparison = new BigDecimal(20000000);

		// when
		boolean actual = NumberUtils.isUnder(target, comparison);

		// then
		Assertions.assertTrue(actual);
	}

	@Test
	@DisplayName("target이 comparison 초과일 때 false 리턴한다")
	void Should_return_false_When_target_value_is_over_comparison() {
		// given
		BigDecimal target = new BigDecimal(20000000);
		BigDecimal comparison = new BigDecimal(10000000);

		// when
		boolean actual = NumberUtils.isUnder(target, comparison);

		// then
		Assertions.assertFalse(actual);
	}

	@Test
	@DisplayName("target이 comparison 같을 때 false 리턴한다")
	void Should_return_false_When_target_values_are_equal() {
		// given
		BigDecimal target = new BigDecimal(20000000);
		BigDecimal comparison = new BigDecimal(20000000);

		// when
		boolean actual = NumberUtils.isUnder(target, comparison);

		// then
		Assertions.assertFalse(actual);
	}

}
