package com.gowid.corp.core.repository.querydsl;

import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.dto.GdsResitrationNumbersRespDto;
import com.gowid.corp.core.dto.IssuedCorpDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CustomCardIssuanceInfoRepository {

    CardIssuanceInfo findByIdx(Long idxCardIssuanceInfo);

    Page<IssuedCorpDto> getIssuedCorporationList(final Pageable pageable);

    Page<GdsResitrationNumbersRespDto> getRegistrationNumbersForBatch(final Pageable pageable);
}
