package com.gowid.corp.core.domain.limit;

import lombok.*;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

@Getter
@Builder
@Embeddable
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
public class ShinhanLimitIncreaseReviewStockholder {
    @Column(columnDefinition = "bit(1) DEFAULT NULL COMMENT '25%이상의 지분을 보유한 개인여부'")
    private Boolean isStockHold25;

    @Column(columnDefinition = "bit(1) DEFAULT NULL COMMENT '최대주주 개인여부'")
    private Boolean isStockholderPersonal;

    @Column(columnDefinition = "varchar(6) DEFAULT NULL COMMENT '주주생년월일'")
    private String stockholderBirth;

    @Column(columnDefinition = "varchar(80) DEFAULT NULL COMMENT '주주영문명'")
    private String stockholderEngName;

    @Column(columnDefinition = "varchar(50) DEFAULT NULL COMMENT '주주명'")
    private String stockholderName;

    @Column(columnDefinition = "varchar(2) DEFAULT NULL COMMENT '주주국적'")
    private String stockholderNation;

    @Column(columnDefinition = "varchar(5) DEFAULT NULL COMMENT '주주지분율'")
    private String stockRate;
}
