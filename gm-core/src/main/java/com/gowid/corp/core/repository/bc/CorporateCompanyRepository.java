package com.gowid.corp.core.repository.bc;

import com.gowid.corp.core.domain.bc.CorporateCompany;
import com.gowid.corp.core.domain.bc.type.AccountType;
import com.gowid.corp.core.domain.bc.type.CorporateCompanyType;
import com.gowid.corp.core.domain.corp.Corp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CorporateCompanyRepository extends JpaRepository<CorporateCompany, Long> {

    Optional<CorporateCompany> findByAccountTypeAndCorp(AccountType accountType, Corp corp);
    Optional<CorporateCompany> findByAccountTypeAndCorpAndStateNotIn(AccountType accountType, Corp corp, List<CorporateCompanyType> state);
    List<CorporateCompany> findByAccountTypeAndCorpAndState(AccountType accountType, Corp corp, CorporateCompanyType state);
    List<CorporateCompany> findByAccountTypeAndCorpAndStateIn(AccountType accountType, Corp corp, List<CorporateCompanyType> state);
    Optional<CorporateCompany> findByCorporateCompanyNumber(String corporateCompanyNumber);
    List<CorporateCompany> findByCorporateCompanyNumberIn(List<String> corporateCompanyNumbers);
    List<CorporateCompany> findByCorpAndState(Corp corp, CorporateCompanyType state);
}
