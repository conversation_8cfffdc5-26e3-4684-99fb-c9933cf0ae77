package com.gowid.corp.core.expense.domain;

import com.gowid.corp.core.domain.audit.BaseTime;
import lombok.*;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Table(name = "card", schema = "expense")
public class ExpenseCard extends BaseTime {
    @Id
    private Long cardId;
    private String alias;
    private String cardNumber;
    private String companyCode;
    private Long limitAmount;
    private Long cardUserId;
    private Long corpId;
    private Long usedAmount;
    private Long remainAmount;
    private String  sleepyn;
    private String  namedyn;
    private String issueDate;
    private String reIssueDate;
    private String cardName;
    private String cardType;
    private String userNm;
    private String validPeriod;
    private String unmaskedCardNumber;
    private Boolean isInvalid;
    private String duplicationStatus;
    private String limitStatus;
    private Integer scrapStatus;
    private String fullCardNumber;
    private LocalDateTime limitUpdatedAt;
    private LocalDate terminatedAt;
    private String unitCardType;
    private String managementNumber;
    private Boolean isCommonCard;
    private String corporateCompanyNumber;
    private String cardProductNumber;
}
