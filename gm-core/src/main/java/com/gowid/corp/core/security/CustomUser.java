package com.gowid.corp.core.security;

import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.user.User;
import java.util.Collection;
import java.util.stream.Collectors;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

@Slf4j
@Getter
@Setter
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true)
public class CustomUser extends org.springframework.security.core.userdetails.User {

    private Long idx;
    private String email;
    private String name;
    private String uriProfileImage;
    private User user;
    private Corp corp;


    public CustomUser(User user) {
        super(
                user.email(), "",
                user.authentication().isEnabled(),
                user.authentication().isAccountNonExpired(),
                user.authentication().isCredentialsNonExpired(),
                user.authentication().isAccountNonLocked(),
                getAuth(user)
        );

        idx = user.idx();
        name = user.name();
        email = user.email();
        this.corp = user.corp();
        this.user = user;
    }

    private static Collection<? extends GrantedAuthority> getAuth(User user) {
        return user.authorities().stream()
                .map(authority -> new SimpleGrantedAuthority(authority.role().name()))
                .collect(Collectors.toList());
    }

    @Override
    public String toString() {
        return String.format("CustomUser(idx=%d, username=%s, authorities=%s)", idx, getUsername(), getAuthorities());
    }
}
