package com.gowid.corp.core.repository.corp;

import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.corp.Product;
import com.gowid.corp.core.domain.corp.ServiceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    Product findByCorpAndServiceType(Corp corp, ServiceType serviceType);
    List<Product> findByCorp(Corp corp);

}
