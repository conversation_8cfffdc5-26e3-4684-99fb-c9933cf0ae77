package com.gowid.corp.core.expense.repository;

import com.gowid.corp.core.expense.domain.ExpenseCard;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ExpenseCardRepository extends JpaRepository<ExpenseCard, Long> {
    Optional<ExpenseCard> findByFullCardNumber(String fullCardNumber);
    Optional<ExpenseCard> findByCardId(Long cardId);
    List<ExpenseCard> findByCorporateCompanyNumber(String corporateCompanyNumber);
}
