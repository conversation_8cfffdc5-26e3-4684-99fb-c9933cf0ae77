package com.gowid.corp.core.issuance.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Builder
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Table(catalog = "officesuite")
public class Application {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 12)
    private String businessRegistrationNumber;

    @Column(nullable = false, length = 80)
    private String corporationName;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 6)
    private Terms.OfficeSuiteType officeSuiteType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 8, columnDefinition = "default 'GWS_PLAN'")
    private IssuanceStage issuanceStage;

    @Column(nullable = false, length = 50)
    private String applicant;

    @Column(nullable = false, length = 40)
    private String position;

    @Column(nullable = false, length = 13)
    private String phoneNumber;

    @Column(nullable = false, columnDefinition = "smallint not null default '5'")
    private Integer paymentDay;

    @Enumerated(EnumType.STRING)
    @Column(length = 28)
    private PlanType planType;

    @Column(precision = 10, scale = 8)
    private BigDecimal discountRate;

    @Column
    private LocalDateTime appliedAt;
    @Column
    private LocalDateTime contractedAt;

    @Column(updatable = false, columnDefinition = "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP")
    protected LocalDateTime createdAt;

    @Column(columnDefinition = "datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    protected LocalDateTime updatedAt;


    @Getter
    @AllArgsConstructor
    public static enum IssuanceStage {
        COUNSEL("상담"),
        GWS_PLAN("플랜 선택"),
        GWS_PLAN_DISCOUNT("할인율 입력"),
        SIGNING("서명"),
        GWS_TOKEN("GWS 토큰 입력"),
        CONTRACT("계약 완료"),
        DROP("드롭"),
        TERMINATION("계약 해지");

        private final String description;
    }

    @AllArgsConstructor
    @Getter
    public static enum PlanType {

        BUSINESS_STARTER_FLEXIBLE("Business Starter", "Flexible 플랜", BigDecimal.valueOf(7.20), BigDecimal.valueOf(0.79), BigDecimal.valueOf(6.41),
            BigDecimal.valueOf(11.00), "FLEXIBLE"),
        BUSINESS_STARTER_ANNUAL("Business Starter", "Annual 플랜", BigDecimal.valueOf(6.00), BigDecimal.valueOf(0.66), BigDecimal.valueOf(5.34),
            BigDecimal.valueOf(11.00), "ANNUAL"),
        BUSINESS_STANDARD_FLEXIBLE("Business Standard", "Flexible 플랜", BigDecimal.valueOf(14.40), BigDecimal.valueOf(1.58), BigDecimal.valueOf(12.82),
            BigDecimal.valueOf(11.00), "FLEXIBLE"),
        BUSINESS_STANDARD_ANNUAL("Business Standard", "Annual 플랜", BigDecimal.valueOf(12.00), BigDecimal.valueOf(1.32), BigDecimal.valueOf(10.68),
            BigDecimal.valueOf(11.00), "ANNUAL"),
        BUSINESS_PLUS_FLEXIBLE("Business Plus", "Flexible 플랜", BigDecimal.valueOf(21.60), BigDecimal.valueOf(2.38), BigDecimal.valueOf(19.22),
            BigDecimal.valueOf(11.00), "FLEXIBLE"),
        BUSINESS_PLUS_ANNUAL("Business Plus", "Annual 플랜", BigDecimal.valueOf(18.00), BigDecimal.valueOf(1.98), BigDecimal.valueOf(16.02),
            BigDecimal.valueOf(11.00), "ANNUAL"),
        ENTERPRISE_STANDARD_FLEXIBLE("Enterprise Standard", "Flexible 플랜", BigDecimal.valueOf(27.60), BigDecimal.valueOf(3.04),
            BigDecimal.valueOf(24.56), BigDecimal.valueOf(11.00), "FLEXIBLE"),
        ENTERPRISE_STANDARD_ANNUAL("Enterprise Standard", "Annual 플랜", BigDecimal.valueOf(23.00), BigDecimal.valueOf(2.53), BigDecimal.valueOf(20.47),
            BigDecimal.valueOf(11.00), "ANNUAL"),
        ENTERPRISE_PLUS_FLEXIBLE("Enterprise Plus", "Flexible 플랜", BigDecimal.valueOf(36.00), BigDecimal.valueOf(3.96), BigDecimal.valueOf(32.04),
            BigDecimal.valueOf(11.00), "FLEXIBLE"),
        ENTERPRISE_PLUS_ANNUAL("Enterprise Plus", "Annual 플랜", BigDecimal.valueOf(30.00), BigDecimal.valueOf(3.30), BigDecimal.valueOf(26.70),
            BigDecimal.valueOf(11.00), "ANNUAL");

        private final String title;
        private final String description;

        private final BigDecimal primeCost;

        private final BigDecimal discountedPrice;

        private final BigDecimal gowidDiscountedPrice;

        private final BigDecimal defaultDiscountRate;

        private final String paymentPlan;


        public String getTitleDescription() {
            return this.title + "(" + this.description + ")";
        }
    }
}
