package com.gowid.corp.core.dto.response;

import com.gowid.corp.core.domain.bc.BillingUnitLimit;
import com.gowid.corp.core.domain.bc.type.ApplicationType;
import com.gowid.corp.core.domain.card.CardCompany;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingUnitLimitRes {
    private ApplicationType billingUnitType;
    private CardCompany cardCompany;
    private Long totalLimit;
    private Long usedLimit;
    private Long remainLimit;
    @Builder.Default
    private double exhaustionRate = 0.0;

    public static BillingUnitLimitRes from(BillingUnitLimit billingUnitLimit) {
        return BillingUnitLimitRes.builder()
                .billingUnitType(billingUnitLimit.billingUnitType())
                .cardCompany(billingUnitLimit.cardCompany())
                .remainLimit(billingUnitLimit.remainLimit())
                .totalLimit(billingUnitLimit.totalLimit())
                .usedLimit(billingUnitLimit.usedLimit())
                .build();
    }
}
