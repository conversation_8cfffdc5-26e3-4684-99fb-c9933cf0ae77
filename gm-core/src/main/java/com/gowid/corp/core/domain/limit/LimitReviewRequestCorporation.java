package com.gowid.corp.core.domain.limit;

import com.gowid.corp.core.domain.audit.BaseTime;
import lombok.*;
import org.hibernate.annotations.DynamicUpdate;

import jakarta.persistence.*;


@Getter
@Setter
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@DynamicUpdate
public class LimitReviewRequestCorporation extends BaseTime {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false, updatable = false)
    private Long idx;

    @Column(columnDefinition = "VARCHAR(255) COMMENT '법인 사업자 번호(번호만)'", nullable = false)
    private String corporationIdentityNumber;

    @Column(columnDefinition = "VARCHAR(255) COMMENT '법인명'", nullable = false)
    private String corporationName;

    @Column(columnDefinition = "bit(1) DEFAULT FALSE COMMENT '재심사 신청 차단 여부'")
    private Boolean isBlocked;

    @Column(columnDefinition = "VARCHAR(255) COMMENT '변경한 사람이름'")
    private String updatedBy;

}
