package com.gowid.corp.core.domain.limit;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.encryption.shinhan.Seed128;
import com.gowid.corp.core.exception.GowidException;
import lombok.*;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Embeddable;
import java.util.regex.Pattern;

@Access(AccessType.FIELD)
@Getter
@Builder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Embeddable
public class ShinhanLimitIncreaseReviewCeoInfo {
    private static final Pattern INIT_RRN_PATTERN = Pattern.compile("^([0-9]){6}-[*]{7}$");

    private String name;
    private String RRN;
    private String engName;
    private String nation;
    private String phoneNumber;
    private String genderCode;

    public boolean validate(String name, String birth) {
        if (this.name.equals(name) && getBirth().equals(birth)) {
            return true;
        }

        throw new GowidException(ResultCode.INVALID_PARAMETER, "대표자 정보가 일치하지 않습니다.");
    }

    public String getBirth() {
        if (this.RRN == null) {
            return null;
        }

        if (Pattern.matches(INIT_RRN_PATTERN.pattern(), this.RRN)) {
            return this.RRN.substring(0, 6);
        }

        return Seed128.decryptEcb(this.RRN).substring(0, 6);
    }

    public void updateRRN(String RRN) {
        this.RRN = RRN;
    }

    public void updateAfterIdentification(String engName, String phoneNumber) {
        phoneNumber = phoneNumber.replace("-", "");
        if (phoneNumber.length() < 11) {
            throw new GowidException(ResultCode.INVALID_PARAMETER, "잘못된 형태의 전화번호입니다.");
        }

        this.engName = engName;
        this.phoneNumber = phoneNumber;
    }

    public boolean isCertificated() {
        return this.RRN != null && !Pattern.matches(INIT_RRN_PATTERN.pattern(), this.RRN);
    }
}
