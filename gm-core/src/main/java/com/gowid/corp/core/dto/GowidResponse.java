package com.gowid.corp.core.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.gowid.corp.core.exception.BaseException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import com.gowid.corp.core.exception.result.ResultType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude
public class GowidResponse<T> implements Serializable {

    private ApiResult result;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalCount;
    private T data;

    public GowidResponse(ApiResult result) {
        this.result = result;
    }

    public GowidResponse(T data) {
        this.data = data;
    }

    public static <T> GowidResponse<T> ok() {
        return new GowidResponse<>(ApiResult.getSuccess());
    }

    public static <T> GowidResponse<T> ok(T data) {
        return new GowidResponse<>(ApiResult.getSuccess(), null, data);
    }

    public static <T> GowidResponse<T> response(T data) {
        return new GowidResponse<>(ApiResult.getSuccess(), null, data);
    }

    public static GowidResponse<GowidMessageGroup> response() {
        return new GowidResponse<>(ApiResult.getSuccess());
    }

    public GowidResponse(BaseException ex) {
        this.result = new ApiResult(ex);
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiResult implements Serializable {
        @Schema(description = "코드", example = "6000")
        public String code;

        @Schema(description = "설명", example = "system error")
        public String desc;

        @Schema(description = "추가 코드", example = "G-11111")
        public String extraCode;

        @Schema(description = "추가 메세지", example = "추가 메세지")
        public String extraMessage;

        public ApiResult(ResultType resultType) {
            this.code = resultType.getCode();
            this.desc = resultType.getDesc();
        }

        public ApiResult(ResultType resultType, String extraMsg) {
            this.code = resultType.getCode();
            this.desc = resultType.getDesc();
            this.extraMessage = extraMsg;
        }

        public ApiResult(ResultType resultType, GowidMessageGroup gowidMessageGroup) {
            this.code = resultType.getCode();
            this.desc = resultType.getDesc();
            this.extraCode = gowidMessageGroup.getCode();
            this.extraMessage = gowidMessageGroup.getMessage();
        }

        public static ApiResult getSuccess() {

            return new ApiResult(ResultType.SUCCESS, GowidMessageGroup.G00000);
        }

        public ApiResult(BaseException ex) {
            this.code = ex.getCode();
            this.desc = ex.getDesc();
            this.extraCode = ex.getExtraCode();
            this.extraMessage = ex.getExtraMessage();
        }

    }


}
