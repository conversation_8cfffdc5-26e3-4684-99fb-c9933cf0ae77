package com.gowid.corp.core.domain.shinhan;

import com.gowid.corp.core.domain.audit.BaseTime;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import lombok.*;
import lombok.experimental.Accessors;

import jakarta.persistence.*;

@Getter
@Setter
@Accessors(fluent = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
public class FinancialConsumerProtectionActShinhan extends BaseTime {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false, updatable = false)
    private Long idx;

    @Column
    private String annualIncomeClassificationCode;

    @Column
    private String annualSalesClassificationCode;

    @Column
    private String creditScoreCode;

    @Column
    private String creditRatingCode;

    @Column
    private Boolean isOverdueWithinThreeMonths;

    @Column
    private Boolean isOverdueWithinThreeMonthsByCorporation;

    @Column
    private Boolean isAgreeToOverdueTerms;

    @Column
    private Boolean isAgreeToEarlyTermination;

    @Column
    private Boolean isAgreeToLimitAdjustment;

    @Column
    private Boolean isAgreeToTheManual;

    @Column
    private Boolean isAgreeToCustomerRightsConsent;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "idxCardIssuanceInfo", foreignKey = @ForeignKey(name = "FK_FinancialConsumerProtectionActShinhan_CardIssuanceInfo"), referencedColumnName = "idx")
    private CardIssuanceInfo cardIssuanceInfo;
}
