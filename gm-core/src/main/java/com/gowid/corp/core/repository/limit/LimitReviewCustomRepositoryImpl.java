package com.gowid.corp.core.repository.limit;

import com.gowid.corp.core.domain.limit.LimitReview;
import com.gowid.corp.core.domain.limit.QLimitReview;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Qualifier;


public class LimitReviewCustomRepositoryImpl implements LimitReviewCustomRepository {

    private final QLimitReview limitReview = QLimitReview.limitReview;
    private final JPAQueryFactory jpaQueryFactory;

    public LimitReviewCustomRepositoryImpl(@Qualifier("gowidQueryFactory") final JPAQueryFactory jpaQueryFactory) {
        this.jpaQueryFactory = jpaQueryFactory;
    }

    @Override
    public LimitReview findLimitReviewOrNull(final Long idxCorp) {
        return jpaQueryFactory.selectFrom(limitReview)
                .where(limitReview.corp.idx.eq(idxCorp))
                .orderBy(limitReview.updatedAt.desc())
                .fetchFirst();
    }

    @Override
    public LimitReview findLimitReviewByCardIssuanceInfo(final Long idxCardIssuanceInfo) {
        return jpaQueryFactory.selectFrom(limitReview)
                .where(limitReview.cardIssuanceInfo.idx.eq(idxCardIssuanceInfo))
                .orderBy(limitReview.updatedAt.desc())
                .fetchFirst();
    }
}
