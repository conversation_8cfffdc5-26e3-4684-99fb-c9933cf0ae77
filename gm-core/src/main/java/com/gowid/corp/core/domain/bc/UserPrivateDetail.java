package com.gowid.corp.core.domain.bc;

import com.gowid.corp.core.domain.cardIssuanceInfo.CertificationType;
import com.gowid.corp.core.domain.corp.Corp;
import lombok.*;
import lombok.experimental.Accessors;

import jakarta.persistence.*;

@Getter
@Setter
@Accessors(fluent = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "UserDetail")
public class UserPrivateDetail {

    @Id
    private Long idxUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "idxCorp", foreignKey = @ForeignKey(name = "FK_Corp_UserDetail"))
    private Corp corp;

    private String birth;
    private String name;
    private String engName;
    private Boolean isForegin;
    private String nationality;
    private Long genderCode;
    private String identificationNumber;
    private String identificationIssuedDate;
    private String drivingLicenseNumber;

    @Enumerated(EnumType.STRING)
    private CertificationType certificationType;
}
