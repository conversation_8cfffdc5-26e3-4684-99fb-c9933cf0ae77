package com.gowid.corp.core.repository.lotte;

import com.gowid.corp.core.domain.lotte.LotteCorpInfoBody;
import com.gowid.corp.core.domain.lotte.LotteCorpInfoBodyPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface LotteCorpInfoBodyRepository extends JpaRepository<LotteCorpInfoBody, LotteCorpInfoBodyPk> {

    @Query(value = "SELECT MAX(CORP_ORT_LIM_AM) " +
            "FROM Lotte_CorpInfoBody " +
            "WHERE fName = (SELECT MAX(fName) FROM Lotte_CorpInfoBody) " +
            "AND BZNO = :BZNO", nativeQuery = true)
    Optional<String> getLatestGrantLimit(@Param("BZNO") String BZNO);
}
