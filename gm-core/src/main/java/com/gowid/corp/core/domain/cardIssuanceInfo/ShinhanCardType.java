package com.gowid.corp.core.domain.cardIssuanceInfo;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ShinhanCardType {
	GREEN("그린카드", "DAACR4", "EA", "0"),
	GREEN_TRAFFIC("그린교통카드", "DAACR4", "EA","1"),
	BLACK("블랙카드", "DAACR4", "E5","0"),
	BLACK_TRAFFIC("블랙교통카드", "DAACR4", "E5","1");

	private final String name; // 카드이름
	private final String type; // 카드상품
	private final String designCode; // 카드상품
	private final String useTraffic; // 교통기능구분코드


}
