package com.gowid.corp.core.repository.querydsl;

import com.gowid.corp.core.domain.bc.type.AccountType;
import com.gowid.corp.core.domain.bc.type.CorporateCompanyType;
import com.gowid.corp.core.dto.response.CorporateCompanyRes;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import static com.gowid.corp.core.domain.bc.QCorporateCompany.corporateCompany;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CustomCorporateCompanyRepositoryImpl implements CustomCorporateCompanyRepository {

    private final JPAQueryFactory jpaQueryFactory;

    // 업체 데이터가 2개이상일 경우 한도가 가장 큰 값 하나만 가져오도록 조회
    @Override
    public CorporateCompanyRes findByAccountType(AccountType type, Long idxCorp) {
        return jpaQueryFactory
                .select(
                        Projections.constructor(
                                CorporateCompanyRes.class,
                                corporateCompany.idx,
                                corporateCompany.corporateCompanyNumber,
                                corporateCompany.corporateCompanyLimit,
                                corporateCompany.accountType,
                                corporateCompany.bankCode,
                                corporateCompany.account,
                                corporateCompany.paymentDay,
                                corporateCompany.accountNickName,
                                corporateCompany.state
                        )
                )
                .from(corporateCompany)
                .where(
                        corporateCompany.accountType.eq(type)
                            .and(corporateCompany.state.eq(CorporateCompanyType.ACTIVE))
                            .and(corporateCompany.corp.idx.eq(idxCorp))
                )
                .orderBy(corporateCompany.corporateCompanyLimit.desc())
                .limit(1L)
                .fetchOne();
    }
}
