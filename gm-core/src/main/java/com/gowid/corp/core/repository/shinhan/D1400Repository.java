package com.gowid.corp.core.repository.shinhan;

import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.common.FullTextTranType;
import com.gowid.corp.core.domain.shinhan.D1400;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface D1400Repository extends JpaRepository<D1400, Long> {

    Optional<D1400> findFirstByCardIssuanceInfoAndTranTypeOrderByUpdatedAtDesc(CardIssuanceInfo cardIssuanceInfo, FullTextTranType tranType);

    D1400 findFirstByD025AndD026OrderByUpdatedAtDesc(String d025, String d026);

}
