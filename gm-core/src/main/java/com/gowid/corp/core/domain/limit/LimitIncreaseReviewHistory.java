package com.gowid.corp.core.domain.limit;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Getter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@EntityListeners(value = {AuditingEntityListener.class})
public class LimitIncreaseReviewHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "bigint not null comment '한도상향 심사 id'")
    private Long limitIncreaseReviewId;

    @Column(columnDefinition = "bigint NOT NULL COMMENT '카드신청정보아이디'")
    private Long idxCardIssuanceInfo;

    @Column(columnDefinition = "varchar(12) NOT NULL COMMENT '사업자등록번호'")
    private String businessRegistrationNumber;

    @Column(columnDefinition = "bigint NOT NULL COMMENT '법인담당자아이디'")
    private Long userId;

    @Column(columnDefinition = "varchar(8) DEFAULT NULL COMMENT '접수일자'")
    private String receptionDate;

    @Column(columnDefinition = "varchar(7) DEFAULT NULL COMMENT '접수순번")
    private String receptionOrder;

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar(20) DEFAULT NULL COMMENT '한도타입'")
    private LimitType limitType;

    @Column(columnDefinition = "decimal(24, 8) null comment '기준잔고'")
    private BigDecimal baseBalance;

    @Column(columnDefinition = "decimal(24, 8) null comment '현재잔고'")
    private BigDecimal currentBalance;

    @Column(columnDefinition = "datetime null comment '기준일자'")
    private LocalDateTime baseDate;

    @Setter
    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '기존한도'")
    private BigDecimal currentLimit;

    @Setter
    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '요청한도'")
    private BigDecimal hopedLimit;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '산출한도'")
    private BigDecimal calculatedLimit;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '투자승수")
    private BigDecimal limitRatio;

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar(20) DEFAULT NULL COMMENT '진행상태'")
    private LimitIncreaseReviewStatus status;

    @Setter
    @Column(columnDefinition = "bit(1) DEFAULT false COMMENT '한도상향 업무처리 위임여부'")
    private Boolean isDelegated;

    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '신청완료일자")
    private LocalDateTime requestedAt;

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar(20) DEFAULT NULL COMMENT '고위드심사결과'")
    private LimitIncreaseReviewResult gowidResult;

    @Column(columnDefinition = "varchar(300) DEFAULT NULL COMMENT '고위드심사의견'")
    private String gowidComment;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '고위드심사한도'")
    private BigDecimal gowidLimit;

    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '고위드 심사완료일자'")
    private LocalDateTime gowidReviewedAt;

    @Column(columnDefinition = "date DEFAULT NULL COMMENT '유예기한'")
    private LocalDate graceExpireDate;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '승인금액'")
    private BigDecimal graceApprovedAmount;

    @Column(columnDefinition = "varchar(300) DEFAULT NULL COMMENT '유예사유'")
    private String graceApprovedReason;

    @Column(columnDefinition = "date DEFAULT NULL COMMENT '계약일자'")
    private LocalDate depositContractDate;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '입금금액'")
    private BigDecimal depositAmount;

    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '카드사 상향전문 전송일자'")
    private LocalDateTime fulltextSentAt;

    @Column(columnDefinition = "decimal(24, 8) DEFAULT NULL COMMENT '카드사부여한도'")
    private BigDecimal cardCompanyLimit;

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar(20) DEFAULT NULL COMMENT '카드사심사결과'")
    private LimitIncreaseReviewResult cardCompanyResult;

    @Column(columnDefinition = "varchar(100) DEFAULT NULL COMMENT '카드사심사의견'")
    private String cardCompanyComment;

    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '심사완료일자'")
    private LocalDateTime cardCompanyReviewedAt;

    @Enumerated(value = EnumType.STRING)
    @Column(columnDefinition = "varchar(20) DEFAULT NULL COMMENT '최종심사결과'")
    private LimitIncreaseReviewResult reviewedResult;

    @Column(columnDefinition = "datetime DEFAULT NULL COMMENT '메일발송일자")
    private LocalDateTime emailedAt;

    @Setter
    @Column(columnDefinition = "bit(1) DEFAULT false COMMENT '결과확인여부'")
    private Boolean isRead;

}
