package com.gowid.corp.core.domain.corp;

import com.gowid.corp.core.domain.audit.BaseTime;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.user.User;
import java.util.ArrayList;
import java.util.List;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Where;
import java.time.LocalDate;
import org.json.simple.JSONObject;

@Getter
@Setter
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Entity
@Builder
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "isDeleted = false")
public class Corp extends BaseTime {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(nullable = false, updatable = false)
	@EqualsAndHashCode.Include
	private Long idx;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "idxUser", foreignKey = @ForeignKey(name = "FK_User_Corp"))
    private User user; // 법인을 등록한 사용자

    @Builder.Default
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "corp")
    private List<CardIssuanceInfo> cardIssuanceInfo = new ArrayList<>();

    private String resBusinessItems; // 종목
    private String resBusinessTypes; // 업태
    private String resBusinessmanType; // 사업자종류
    private String resBusinessCode; // 업종코드

    @EqualsAndHashCode.Include
    private String resCompanyIdentityNo; // 사업자등록번호

    private String resCompanyNm; // 법인명
    private String resCompanyEngNm; // 법인명(영문)
    private String resCompanyNumber; // 사업장전화번호
    private String resCompanyZipCode; // 사업장우편번호
    private String resCompanyAddr; // 사업장우편번호주소
    private String resCompanyAddrDt; // 사업장우편번호외주소
    private String resCompanyBuildingCode; // 도로명참조키값
    private String resIssueNo; // 발급(승인)번호
    private String resIssueOgzNm; // 발급기관
    private String resJointIdentityNo; //공동사업자 주민번호
    private String resJointRepresentativeNm; // 공동사업자 성명(법인명)
    private String resOpenDate; // 개업일
    private String resOriGinalData; // 원문 DATA
    private String resRegisterDate; // 사업자등록일
    private String resUserAddr; // 사업장소재지(주소)
    private String resUserIdentiyNo; // 주민(법인)등록번호
    private String resUserNm; // 성명(대표자)
    private String resUserType; // 대표자 종류 (1: 개별, 2:각자, 3:공동)
    private Integer ceoCount; // 대표자 수
    private Integer employeeCount;      // 고용인 수
    private String employeeCountDate;   // 고용인 수 조회 일자

    @Enumerated(EnumType.STRING)
    private CorpStatus status; // pending/denied/approved

    private Boolean isAgreeLimitIncreaseNoti;
    private Boolean isDeleted; // 삭제 여부


    @Builder.Default
    @Column(columnDefinition = "bit(1) default false")
    private Boolean isExited = Boolean.FALSE;  // 탈회 여부

    @Column(columnDefinition = "date default null")
    private LocalDate exitDate;  // 탈회 일자


    @Column(nullable = false, columnDefinition = "varchar(30) default 'GOWID'")
    private String tenantKey; // 테넌트 타입

    public void updateCorpInfo(JSONObject jsonData) {
        this.resJointRepresentativeNm(extractValueFromJson(jsonData, "resJointRepresentativeNm"));
        this.resIssueOgzNm(extractValueFromJson(jsonData, "resIssueOgzNm"));
        this.resCompanyNm(extractValueFromJson(jsonData, "resCompanyNm"));
        this.resBusinessTypes(extractValueFromJson(jsonData, "resBusinessTypes"));
        this.resBusinessItems(extractValueFromJson(jsonData, "resBusinessItems"));
        this.resBusinessmanType(extractValueFromJson(jsonData, "resBusinessmanType"));
        this.resCompanyIdentityNo(extractValueFromJson(jsonData, "resCompanyIdentityNo"));
        this.resIssueNo(extractValueFromJson(jsonData, "resIssueNo"));
        this.resJointIdentityNo(extractValueFromJson(jsonData, "resJointIdentityNo"));
        this.resOpenDate(extractValueFromJson(jsonData, "resOpenDate"));
        this.resOriGinalData(extractValueFromJson(jsonData, "resOriGinalData"));
        this.resRegisterDate(extractValueFromJson(jsonData, "resRegisterDate"));
        this.resUserAddr(extractValueFromJson(jsonData, "resUserAddr"));
        this.resUserIdentiyNo(extractValueFromJson(jsonData, "resUserIdentiyNo"));
        this.resUserNm(extractValueFromJson(jsonData, "resUserNm"));
        this.resCompanyNumber(extractValueFromJson(jsonData, "resPhoneNo"));
        this.isDeleted(false);
    }

    private String extractValueFromJson(final JSONObject resultJson, final String key) {
        if (resultJson != null && resultJson.get(key) != null) {
            return resultJson.get(key).toString().trim();
        } else {
            return "";
        }
    }

    public void updateCorpBusinessInfoUpdate(JSONObject jsonData) {
        this.resJointRepresentativeNm(extractValueFromJson(jsonData, "resJointRepresentativeNm"));
        this.resIssueOgzNm(extractValueFromJson(jsonData, "resIssueOgzNm"));
        this.resCompanyNm(extractValueFromJson(jsonData, "resCompanyNm"));
        this.resBusinessTypes(extractValueFromJson(jsonData, "resBusinessTypes"));
        this.resBusinessItems(extractValueFromJson(jsonData, "resBusinessItems"));
        this.resBusinessmanType(extractValueFromJson(jsonData, "resBusinessmanType"));
        this.resCompanyIdentityNo(extractValueFromJson(jsonData, "resCompanyIdentityNo"));
        this.resIssueNo(extractValueFromJson(jsonData, "resIssueNo"));
        this.resJointIdentityNo(extractValueFromJson(jsonData, "resJointIdentityNo"));
        this.resOpenDate(extractValueFromJson(jsonData, "resOpenDate"));
        this.resOriGinalData(extractValueFromJson(jsonData, "resOriGinalData"));
        this.resRegisterDate(extractValueFromJson(jsonData, "resRegisterDate"));
        this.resUserAddr(extractValueFromJson(jsonData, "resUserAddr"));
        this.resUserIdentiyNo(extractValueFromJson(jsonData, "resUserIdentiyNo"));
        this.resUserNm(extractValueFromJson(jsonData, "resUserNm"));
    }
}
