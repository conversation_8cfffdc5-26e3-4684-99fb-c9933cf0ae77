package com.gowid.corp.core.repository.querydsl;

import com.gowid.corp.core.domain.consent.Consent;
import com.gowid.corp.core.domain.consent.QConsent;
import com.querydsl.jpa.impl.JPAQueryFactory;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class CustomConsentRepositoryImpl implements CustomConsentRepository{

    private final JPAQueryFactory jpaQueryFactory;
    private final QConsent qConsent = QConsent.consent;

    @Override
    public List<Long> getIdsByTypeCode(String typeCode) {
        return jpaQueryFactory.select(qConsent.idx)
            .from(qConsent)
            .where(qConsent.typeCode.eq(typeCode).and(qConsent.enabled.eq(true)))
            .orderBy(qConsent.consentOrder.asc())
            .fetch();
    }

    @Override
    public List<Consent> getConsentList(String typeCode) {
        return jpaQueryFactory.select(qConsent)
            .from(qConsent)
            .where(qConsent.typeCode.eq(typeCode).and(qConsent.enabled.eq(true)))
            .orderBy(qConsent.consentOrder.asc())
            .fetch();
    }
}
