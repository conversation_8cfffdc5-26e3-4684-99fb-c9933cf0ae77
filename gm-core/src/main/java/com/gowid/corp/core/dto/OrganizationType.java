package com.gowid.corp.core.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum OrganizationType {

    BANK("BK", "은행"),
    CARD("CD", "카드"),
    STOCK("ST", "증권"),
    PUBLIC("NT", "공공기관"),
    UNKNOWN("NON", "알 수 없음")
    ;

    private static final Map<String, OrganizationType> typeCodeMap = Collections.unmodifiableMap(
            Arrays.stream(OrganizationType.values()).collect(Collectors.toMap(OrganizationType::getCode, Function.identity()))
    );
    private static final Map<String, OrganizationType> typeDescMap = Collections.unmodifiableMap(
            Arrays.stream(OrganizationType.values()).collect(Collectors.toMap(OrganizationType::getDesc, Function.identity()))
    );

    private final String code;
    private final String desc;

    public static OrganizationType getByCode(String code) {
        return typeCodeMap.getOrDefault(code, null);
    }

    public static OrganizationType getByDesc(String desc) {
        return typeDescMap.getOrDefault(desc, null);
    }

}
