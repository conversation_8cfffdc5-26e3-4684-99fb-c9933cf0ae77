package com.gowid.corp.core.domain.limit;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LimitReviewActivity {

    UPDATE_REVIEW_COMMENT("심사의견 임시저장"),
    UPDATE_REVIEW_GRANT("심사 - 한도 부여"),
    UPDATE_REVIEW_REFUSE("심사 - 한도 미부여"),
    UPDATE_REVIEW_HOLDING("심사 - 홀딩"),
    UPDATE_REVIEW_CANCEL("심사 - 철회"),
    UPDATE_APPROVAL_APPROVE("최종승인 - 승인"),
    UPDATE_APPROVAL_REJECT("최종승인 - 부결"),
    DO_NOTHING("기본값");

    private final String name;

}
