package com.gowid.corp.core.domain.limit;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ShinhanLimitIncreaseReviewResponseCode {
    APPROVED("00", "전액 승인"),
    REJECTED("03", "부결");

    private String code;
    private String desc;

    public boolean isApproved() {
        return this == APPROVED;
    }

    public static boolean isValidCode(String code) {
        return APPROVED.getCode().equals(code) || REJECTED.getCode().equals(code);
    }

    public static ShinhanLimitIncreaseReviewResponseCode match(String code) {
        return Arrays.stream(values())
                .filter(responseCode -> responseCode.getCode().equals(code))
                .findFirst()
                .orElseThrow(() -> new GowidException(ResultCode.BAD_REQUEST, "신한 1800 전문의 응답은 00과 03만 가능합니다."));
    }
}
