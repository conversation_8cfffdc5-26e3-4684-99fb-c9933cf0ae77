package com.gowid.corp.core.repository.cardIssuanceInfo;

import com.gowid.corp.core.domain.card.CardCompany;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.cardIssuanceInfo.CardType;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceDepth;
import com.gowid.corp.core.domain.cardIssuanceInfo.IssuanceStatus;
import com.gowid.corp.core.domain.corp.Corp;
import com.gowid.corp.core.domain.user.User;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CardIssuanceInfoRepository extends JpaRepository<CardIssuanceInfo, Long> {

    Optional<CardIssuanceInfo> findByCorpAndCardType(Corp corp, CardType cardType);

    Optional<CardIssuanceInfo> findByCorpAndIssuanceStatusNot(Corp corp, IssuanceStatus issuanceStatus);

    Optional<CardIssuanceInfo> findByCorpAndCardTypeAndIssuanceStatusNot(Corp corp, CardType cardType, IssuanceStatus issuanceStatus);

    Optional<CardIssuanceInfo> findByCorpAndCardTypeAndCardCompany(Corp corp, CardType cardType, CardCompany cardCompany);

    Optional<CardIssuanceInfo> findByCorpAndCardTypeAndIssuanceDepthNotIn(Corp corp, CardType cardType, List<IssuanceDepth> issuanceDepths);

    Optional<CardIssuanceInfo> findTopByCorpResCompanyIdentityNoOrderByUpdatedAtDesc(String resCompanyIdentityNo);

    Optional<CardIssuanceInfo> findByIdx(Long idx);

    Optional<CardIssuanceInfo> findByIdxAndCorpIdx(Long idxCardIssuance, Long idxCorp);

    Optional<CardIssuanceInfo> findByUserAndCardType(User user, CardType cardType);

    Optional<CardIssuanceInfo> findByUserAndCardCompany(User user, CardCompany cardCompany);

    Optional<CardIssuanceInfo> findByUserAndCardCompanyAndCardType(User user, CardCompany cardCompany, CardType cardType);

    Optional<CardIssuanceInfo> findByCorpAndCardCompanyAndCardType(Corp corp, CardCompany cardCompany, CardType cardType);

    Optional<CardIssuanceInfo> findByCorpAndCardCompany(Corp corp, CardCompany cardCompany);

    Optional<List<CardIssuanceInfo>> findAllByUser(User user);

    List<CardIssuanceInfo> findAllByUserIdx(Long idxUser);

    Optional<List<CardIssuanceInfo>> findAllByCorp(Corp corp);

    Optional<CardIssuanceInfo> findByIdxAndUser(Long idxCardIssuanceInfo, User user);

    Optional<CardIssuanceInfo> findByIdxAndCardCompany (Long idxCardIssuance, CardCompany cardCompany);

    Optional<CardIssuanceInfo> findByIdxCardLimitApplicationStatus(Long idxCardLimitApplicationStatus);
}
