package com.gowid.corp.core.config;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.sql.MySQLTemplates;
import com.querydsl.sql.SQLQueryFactory;
import com.querydsl.sql.SQLTemplates;
import com.querydsl.sql.spring.SpringExceptionTranslator;
import com.querydsl.sql.types.DateTimeType;
import com.querydsl.sql.types.LocalDateType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import javax.sql.DataSource;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class QuerydslConfiguration {

	@Qualifier("gowidDataSource")
	private final DataSource dataSource;

	@PersistenceContext(unitName = "gowid")
	private EntityManager gowidEntityManager;

	@PersistenceContext(unitName = "issuance")
	private EntityManager issuanceEntityManager;

	@PersistenceContext(unitName = "bccard")
	private EntityManager bcCardEntityManager;

	@PersistenceContext(unitName = "expense")
	private EntityManager expenseEntityManager;

	@Bean
	@Primary
	public JPAQueryFactory gowidQueryFactory() {
		return new JPAQueryFactory(gowidEntityManager);
	}

	@Bean
	public JPAQueryFactory issuanceQueryFactory() {
		return new JPAQueryFactory(issuanceEntityManager);
	}

	@Bean
	public JPAQueryFactory bcCardEntityManager() {return new JPAQueryFactory(bcCardEntityManager);}

	@Bean
	public JPAQueryFactory expenseEntityManager() {return new JPAQueryFactory(expenseEntityManager);}

	@Bean
	@Primary
	public NamedParameterJdbcTemplate namedParameterJdbcTemplate() {
		return new NamedParameterJdbcTemplate(dataSource);
	}

	@Bean
	@Primary
	public SQLTemplates mySqlTemplates() {
		return new MySQLTemplates();
	}

	@Bean
	@Primary
	public SQLQueryFactory sqlQueryFactory(@Qualifier("gowidDataSource") DataSource dataSource, SQLTemplates sqlTemplates) {
		com.querydsl.sql.Configuration configuration = new com.querydsl.sql.Configuration(sqlTemplates);
		configuration.setExceptionTranslator(new SpringExceptionTranslator());
		configuration.register(new DateTimeType());
		configuration.register(new LocalDateType());

		return new SQLQueryFactory(configuration, dataSource);
	}

}
