package com.gowid.corp.core.repository.lotte;

import com.gowid.corp.core.domain.cardIssuanceInfo.CardIssuanceInfo;
import com.gowid.corp.core.domain.lotte.FinancialConsumerProtectionActLotte;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FinancialConsumerProtectionActLotteRepository extends JpaRepository<FinancialConsumerProtectionActLotte, Long> {
    Optional<FinancialConsumerProtectionActLotte> findByCardIssuanceInfo(CardIssuanceInfo cardIssuanceInfo);

}
