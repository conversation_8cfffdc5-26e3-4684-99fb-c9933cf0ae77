package com.gowid.corp.core.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.gowid.corp.core.exception.BaseException;
import com.gowid.corp.core.exception.GowidException;
import com.gowid.corp.core.exception.response.GowidMessageGroup;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude
public class GowidResponseV2<T> implements Serializable {

    private ResponseObject result;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalCount;

    @JsonInclude(JsonInclude.Include.ALWAYS)
    private T data;

    public GowidResponseV2(ResponseObject result) {
        this.result = result;
    }

    public GowidResponseV2(T data) {
        this.data = data;
    }

    public static <T> GowidResponseV2<T> ok() {
        return new GowidResponseV2<>(ResponseObject.getSuccess());
    }

    public static <T> GowidResponseV2<T> ok(T data) {
        return new GowidResponseV2<>(ResponseObject.getSuccess(), null, data);
    }

    public static <T> GowidResponseV2<T> response(T data) {
        return new GowidResponseV2<>(ResponseObject.getSuccess(), null, data);
    }

    public static GowidResponseV2<GowidMessageGroup> response() {
        return new GowidResponseV2<>(ResponseObject.getSuccess());
    }

    public GowidResponseV2(GowidException ex) {
        this.result = new ResponseObject(ex);
        this.result.setDesc(ex.getMessage());
    }

    public GowidResponseV2(BaseException ex) {
        this.result = new ResponseObject(ex);
    }

}
