package com.gowid.corp.core.utils;

import com.gowid.corp.core.dto.response.ResultCode;
import com.gowid.corp.core.exception.GowidException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.SecureRandom;
import java.util.Random;

@Slf4j
@UtilityClass
public class AESUtil {
    private static final int ITERATION_COUNT = 20000;
    private static final String CIPHER_PADDING = "AES/CBC/PKCS5Padding";
    private static final String CIPHER_NOPADDING  = "AES/GCM/NoPadding";
    private static final String SECRET_KEY_SPEC = "AES";
    private static final String SECRET_ALGORITHM = "PBKDF2WithHmacSHA1";
    private static final Random RANDOM = new SecureRandom();

    /**
     * AES 복호화 (코드에프 방식)
     */
    public static String decryptWithCodef(String msg, String key) {
        try {
            byte[] keyData = key.getBytes();
            String iv = key.substring(0, 16);

            SecretKey secureKey = new SecretKeySpec(keyData, SECRET_KEY_SPEC);
            Cipher c = Cipher.getInstance(CIPHER_PADDING);
            c.init(Cipher.DECRYPT_MODE, secureKey, new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8)));

            byte[] byteStr = Base64.decodeBase64(msg.getBytes());

            return new String(c.doFinal(byteStr), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException();
        }
    }

    /**
     * AES 암호화 / 랜덤 salt (인증서 서버 방식)
     * 비교 조회해야 하지 않아도 되는 필드
     */
    public static String encryptRandomWithCertificationServer(String msg, String key) {
        byte[] bytes = new byte[32];
        RANDOM.nextBytes(bytes);
        return makeEncrypt(msg, key, bytes);
    }

    /**
     * AES 암호화 / 고정된 salt (인증서 서버 방식)
     * 비교 조회해야 되는 필드
     */
    public static String encryptWithCertificationServer(String msg, String key) {
        byte[] bytes = new byte[]{(byte) 0x43, (byte) 0x50, (byte) 0x47, (byte) 0x57, (byte) 0x30, (byte) 0x37, (byte) 0x30, (byte) 0x32,
                (byte) 0x38, (byte) 0x38, (byte) 0x43, (byte) 0x50, (byte) 0x41, (byte) 0x55, (byte) 0x47, (byte) 0x57,
                (byte) 0x43, (byte) 0x50, (byte) 0x47, (byte) 0x57, (byte) 0x30, (byte) 0x37, (byte) 0x30, (byte) 0x32,
                (byte) 0x38, (byte) 0x38, (byte) 0x43, (byte) 0x50, (byte) 0x41, (byte) 0x55, (byte) 0x47, (byte) 0x57};
        return makeEncrypt(msg, key, bytes);
    }

    /**
     * 지출 DB에 있는 card_company_codef의 id, pw 데이터 복호화
     * @param msg 복호화할 문자열
     * @param key 복호화 시 사용할 key 값
     * @return
     */
    public static String decryptFromExpense(String msg, String key) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_NOPADDING);
            ByteBuffer buffer = ByteBuffer.wrap(java.util.Base64.getDecoder().decode(msg));

            byte[] saltBytes = new byte[32];
            buffer.get(saltBytes, 0, saltBytes.length);
            byte[] ivBytes = new byte[cipher.getBlockSize()];
            buffer.get(ivBytes, 0, ivBytes.length);
            byte[] encryptedTextBytes = new byte[buffer.capacity() - saltBytes.length - ivBytes.length];
            buffer.get(encryptedTextBytes);

            SecretKeyFactory factory = SecretKeyFactory.getInstance(SECRET_ALGORITHM);
            PBEKeySpec spec = new PBEKeySpec(key.toCharArray(), saltBytes, 1000, 256);

            SecretKey secretKey = factory.generateSecret(spec);
            SecretKeySpec secret = new SecretKeySpec(secretKey.getEncoded(), SECRET_KEY_SPEC);
            GCMParameterSpec ivSpec = new GCMParameterSpec(16 * Byte.SIZE, key.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, secret, ivSpec);

            byte[] decryptedTextBytes = cipher.doFinal(encryptedTextBytes);
            return new String(decryptedTextBytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new GowidException(ResultCode.EXCEPTION, "## Decryption failed!");
        }
    }

    /**
     * AES 복호화 (인증서 서버 방식)
     */
    public static String decryptWithCertificationServer(String msg, String key) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_NOPADDING);
            ByteBuffer buffer = ByteBuffer.wrap(Base64.decodeBase64(msg));

            byte[] saltBytes = new byte[32];
            buffer.get(saltBytes, 0, saltBytes.length);
            byte[] ivBytes = new byte[cipher.getBlockSize()];
            buffer.get(ivBytes, 0, ivBytes.length);
            byte[] encryptedTextBytes = new byte[buffer.capacity() - saltBytes.length - ivBytes.length];
            buffer.get(encryptedTextBytes);

            SecretKeyFactory factory = SecretKeyFactory.getInstance(SECRET_ALGORITHM);
            PBEKeySpec spec = new PBEKeySpec(key.toCharArray(), saltBytes, ITERATION_COUNT, 256);

            SecretKey secretKey = factory.generateSecret(spec);
            SecretKeySpec secret = new SecretKeySpec(secretKey.getEncoded(), SECRET_KEY_SPEC);
            GCMParameterSpec ivSpec = new GCMParameterSpec(16 * Byte.SIZE, key.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, secret, ivSpec);

            byte[] decryptedTextBytes = cipher.doFinal(encryptedTextBytes);
            return new String(decryptedTextBytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException();
        }
    }

    private static String makeEncrypt(String msg, String key, byte[] bytes) {
        byte[] ivBytes;
        byte[] encryptedTextBytes;
        byte[] buffer;
        try {
            SecretKeyFactory factory = SecretKeyFactory.getInstance(SECRET_ALGORITHM);
            // TODO: iterationCount는 20000 ~ 50000 속도 체크하면서 결정
            // fyi; https://www.notion.so/teamgowid/Migration-e8118ffe7956486083bddcdfd219d035#d278e732e93244368bd7d801f67c8bf4
            PBEKeySpec spec = new PBEKeySpec(key.toCharArray(), bytes, ITERATION_COUNT, 256);

            SecretKey secretKey = factory.generateSecret(spec);
            SecretKeySpec secret = new SecretKeySpec(secretKey.getEncoded(), SECRET_KEY_SPEC);

            Cipher cipher = Cipher.getInstance(CIPHER_NOPADDING);
            GCMParameterSpec ivSpec = new GCMParameterSpec(16 * Byte.SIZE, key.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, secret, ivSpec);

            AlgorithmParameters params = cipher.getParameters();

            ivBytes = params.getParameterSpec(GCMParameterSpec.class).getIV();
            encryptedTextBytes = cipher.doFinal(msg.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException();
        }
        buffer = new byte[bytes.length + ivBytes.length + encryptedTextBytes.length];
        System.arraycopy(bytes, 0, buffer, 0, bytes.length);
        System.arraycopy(ivBytes, 0, buffer, bytes.length, ivBytes.length);
        System.arraycopy(encryptedTextBytes, 0, buffer, bytes.length + ivBytes.length, encryptedTextBytes.length);
        return Base64.encodeBase64String(buffer);
    }

}