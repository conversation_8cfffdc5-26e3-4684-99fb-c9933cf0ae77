package com.gowid.corp.core.domain.etc;

import com.gowid.corp.core.domain.audit.BaseTime;
import lombok.*;

import jakarta.persistence.*;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessClassification extends BaseTime {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false, updatable = false)
    private Long idx;

    // 업종 설명
    @Column(columnDefinition = "varchar(30) NOT NULL COMMENT '업종 설명'")
    private String description;
}
