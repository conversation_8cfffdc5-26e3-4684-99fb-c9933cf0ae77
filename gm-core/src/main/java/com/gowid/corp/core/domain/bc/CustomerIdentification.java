package com.gowid.corp.core.domain.bc;

import com.gowid.corp.core.domain.bc.type.CustomerIdentificationStatusType;
import lombok.*;
import lombok.experimental.Accessors;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(fluent = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
public class CustomerIdentification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false, updatable = false)
    private Long idx;

    @Builder.Default
    private String transactionType = "00"; // default: 00
    private String customerType; // 04 : 지정자, 07 : 대리인
    @Builder.Default
    private String relationshipWithSelf = "임직원"; // Default : 임직원
    @Builder.Default
    private String nationality = "KR"; // 국가 코드 - 04 -> userDetail, 07 -> ManagerInfo
    private String korName; // 한글 이름
    private String engName; // 영문 이름 - 외국인 필수
    private String dateOfBirth; // YYYY-MM-DD - 외국인 일때만 필수
    @Builder.Default
    private String addressType = "01"; // Default 01 (직장), 00 (자택)
    private String address; // 주소
    private String mobilePhoneNumber; // 전화 번호 customerType에 따른 정보 수집 테이블 참고
    private String email; // 이메일 주소 - 카드 신청 시 로그인한 이메일 주소'
    @Builder.Default
    private String occupation = "00"; // default 00 - 급여 소득자
    private String realNameCertificate; // 00 - 주민등록증, 01 - 운전면허증, 04 - 외국인등록증
    @Builder.Default
    private String verificationMethod = "00"; //  Default 00 - (주민등록증, 운전면허증)
    private String issueDate; // YYYY-MM-DD (주민등록증, 운전면허증, 외국인등록증)
    private String identityNumber; // 주민번호
    private String drivingLicenseNumber; // 운전면허번호
    @Enumerated(EnumType.STRING)
    private CustomerIdentificationStatusType state;
    private String originalPath;

    private LocalDateTime createdAt;
}
