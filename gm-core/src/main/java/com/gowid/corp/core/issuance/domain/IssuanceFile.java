package com.gowid.corp.core.issuance.domain;

import com.gowid.corp.core.domain.audit.BaseTime;
import com.gowid.corp.core.utils.DateUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Getter
@Entity
@NoArgsConstructor
@ToString
public class IssuanceFile extends BaseTime {

    private static final String PDF_FILE_EXT = "pdf";
    private static final String TIF_FILE_EXT = "tif";
    private static final String BPR_GOWID_ID = "GOWID1";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long issuanceId; // 카드 발급 id (구: gowid.CardIssuanceInfo, 신: card.issuance)

    @Enumerated(EnumType.STRING)
    private FileType type; //파일 유형

    private String name; // 파일명

    private String originalPath; //원본 파일 경로

    private String transferPath; //카드사 전용 변환 경로


    // 이 아래로는 신규 카드 발급에서 쓰임
    private Long createdBy; // 생성자 id

    private Long updatedBy; // 수정자 id

    private Boolean isDeleted; //삭제 여부

    private LocalDateTime deletedAt; // 삭제 시간

    @Builder
    private IssuanceFile(final Long id, final Long issuanceId, final FileType type, final String name, final String originalPath, final String transferPath, final Long createdBy, final Long updatedBy, final Boolean isDeleted, final LocalDateTime deletedAt) {
        this.id = id;
        this.issuanceId = issuanceId;
        this.type = type;
        this.name = name;
        this.originalPath = originalPath;
        this.transferPath = transferPath;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.isDeleted = isDeleted;
        this.deletedAt = deletedAt;
    }

    public IssuanceFile transferImage(final String imageKey, final int idx, final LocalDateTime now) {
        this.transferPath = makeTransferImageName(imageKey, this.name, idx, now);
        return this;
    }

    private String makeTransferImageName(String imageKey, String originFileName, int idx, LocalDateTime now) {
        final String currentDate = DateUtils.convertDateTimeFormat(now, "yyyyMMddHHmmssSSS");
        return getImageFileName(imageKey, currentDate, idx, getExt(originFileName));
    }

    private String getExt(final String originFileName) {
        final String[] splitFileName = originFileName.split("\\.");
        if (splitFileName.length == 2 && PDF_FILE_EXT.equalsIgnoreCase(splitFileName[1])) {
            return PDF_FILE_EXT;
        }
        return TIF_FILE_EXT;
    }

    private String getImageFileName(String bprImageKey, String currentDate, int index, String ext) {
        return String.format("%s%s%s%02d.%s", bprImageKey, currentDate.substring(8, 14), BPR_GOWID_ID, index, ext);
    }

    public void updateTransferPath(final String transferPath) {
        this.transferPath = transferPath;
    }


    //		{접수번호:14}_{사업자번호:10}_{등록일자:8}_CPBA99_{폼코드:4}_{SEQ:3}.zip
    public String getLotteTransferFileName(final String fullTextRegistrationNumber, final String businessRegistrationNumber,
                                           final String yyyyMMdd, final String seq) {
        final String[] split = this.name.split("\\.");
        String ext = split[split.length - 1];

        StringBuilder stringBuilder = new StringBuilder();
        return stringBuilder.append(fullTextRegistrationNumber)
                .append("_")
                .append(businessRegistrationNumber.replace("-", ""))
                .append("_")
                .append(yyyyMMdd)
                .append("_CPBA99_")
                .append(type.getLotteCode())
                .append("_")
                .append(seq)
                .append(".")
                .append(ext)
                .toString();
    }
}
