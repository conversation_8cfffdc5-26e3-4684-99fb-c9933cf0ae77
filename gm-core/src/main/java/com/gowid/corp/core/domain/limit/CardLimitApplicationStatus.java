package com.gowid.corp.core.domain.limit;

import lombok.*;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "CardLimitApplicationStatus")
public class CardLimitApplicationStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long idxCorp;

    // FK-like field name in the table
    @Column(name = "limitApplicationId", nullable = false)
    private Long limitApplicationId;

    @Column(nullable = false, length = 10)
    private String cardCompany;

    @Column(nullable = false, length = 30)
    private String status; // e.g., APPLICATION_SUBMITTED, etc.

    @Column(nullable = false, length = 20)
    private String applicationType; // e.g., AUTO, PRECISION
    private LocalDateTime applicationSentAt;
    private LocalDateTime opinionSentAt;
    private String opinionFilePath;

    private Boolean isDeleted;

    private String modifier;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}