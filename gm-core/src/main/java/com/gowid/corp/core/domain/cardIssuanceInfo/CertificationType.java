package com.gowid.corp.core.domain.cardIssuanceInfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CertificationType {
	RESIDENT("주민등록증", "101", "01"),
	DRIVER("운전면허증", "201", "02"),
	FOREIGN("외국인등록증", "301", "06"),
	;

	private String description;
	private String shinhanCode;
	private String lotteCode;

	public static String convertBcCustomerIdentificationToCertificationType(CertificationType certificationType) {
		switch (certificationType){
			case RESIDENT:
				return "00";
			case DRIVER:
				return "01";
			case FOREIGN:
				return "04";
			default:
				return "";
		}
	}
}
