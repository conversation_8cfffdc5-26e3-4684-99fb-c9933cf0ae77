package com.gowid.corp.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class Common {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificateDto {

        @Schema(description = "패스워드")
        private String password;

        @Schema(description = "인증서이름")
        private String name;

        @Schema(description = "발급일")
        private String startDate;

        @Schema(description = "만료일")
        private String endDate;

        @Schema(description = "설명1")
        private String desc1;

        @Schema(description = "설명2")
        private String desc2;

        @Schema(description = "인증서발행기관")
        private String issuer;

        @Schema(description = "SN 키값")
        private String serial;

        @Schema(description = "certFile")
        private String certFile;
    }

}
