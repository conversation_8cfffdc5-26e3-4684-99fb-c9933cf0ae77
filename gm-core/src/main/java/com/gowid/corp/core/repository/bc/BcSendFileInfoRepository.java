package com.gowid.corp.core.repository.bc;

import com.gowid.corp.core.domain.bc.BcSendFileInfo;
import com.gowid.corp.core.domain.bc.type.SendStateType;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BcSendFileInfoRepository extends JpaRepository<BcSendFileInfo, Long> {
    List<BcSendFileInfo> findALlBySendState(SendStateType type);

    @Query("SELECT b FROM BcSendFileInfo b WHERE b.idxCardApplicationForm = :idxCardApplicationForm " +
            "AND b.originFilePath like CONCAT(:prefix, '%') AND b.bucket = :bucket")
    List<BcSendFileInfo> findAllByIdxCardApplicationFormAndOriginFilePathLike(@Param("idxCardApplicationForm") Long idxCardApplicationForm, @Param("prefix") String prefix, @Param("bucket") String bucket);
}
