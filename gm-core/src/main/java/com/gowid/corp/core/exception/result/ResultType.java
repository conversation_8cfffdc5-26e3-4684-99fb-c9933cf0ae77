package com.gowid.corp.core.exception.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 결과값 enum
 * https://docs.google.com/spreadsheets/d/1tluQlIS4U8VQX2cx2SNVSUjNvtsgJuhp8RHnjZ18qpM/edit#gid=0
 */
@Getter
@AllArgsConstructor
public enum ResultType {

    // TODO: 에러 메세지 한글 변경 필요.
    //       특정 에러 메세지 사용 때 마다 변경 부탁드립니다!
    SUCCESS("0000", "success"),
    PARTIAL_SUCCESS("0001", "partial success"),

    MISSING_REQUIRED_VALUE("1000", "missing required value"),
    INVALID_DATA("1001", "유효하지 않은 데이터입니다."),
    NOT_FOUND("1002", "요청하신 데이터를 찾을 수 없습니다."),
    DUPLICATED_REQUEST("1003", "duplicated request"),
    DUPLICATED_DATA("1004", "중복된 데이터가 존재합니다."),
    ALREADY_EXIST_CORP("1005", "이미 인증서 연동된 법인입니다."),
    EMPTY_CORP("1006", "법인 정보가 없습니다.\n고객센터로 문의해주세요."),

    AUTHENTICATION_FAILURE("2001", "authentication failure"),
    NO_PERMISSION("2002", "no permission"),
    EXPIRED("2003", "expired"),
    NOT_SIGN_AVAILABLE("2004", "전자서명 가능한 단계 혹은 상태가 아닙니다."),
    LAST_LOGIN_OVER_MULTIPLE_DAYS("2005", "마지막 로그인 일시가 90일 경과하여, 비활성화 조치 되었습니다."),
    EMPLOYEE_STATUS_IS_NOT_AVAILABLE("2006", "비활성화 상태의 계정입니다."),
    LOGIN_FAIL("2007", "로그인 정보가 일치하지 않습니다."),
    EXCEED_THE_NUMBER_OF_SIGN_IN_FAIL("2008", "비밀번호 입력 5회 실패로 인한 계정 잠금입니다."),
    NEED_TO_CHANGE_PASSWORD("2009", "패스워드 변경이 필요합니다."),

    CODEF_ERROR("3100", "codef error"),
    SHINHAN_CARD_ERROR("3200", "shinhan card error"),
    LOTTE_CARD_ERROR("3300", "lotte card error"),

    INTERNAL_RPC_ERROR("5000", "내부 RPC 오류입니다."),
    EXTERNAL_RPC_ERROR("5001", "외부 RPC 오류입니다."),
    CARD_DOCUMENT_ERROR("5002", "전문 송/수신 중 오류가 발생했습니다."),

    SYSTEM_ERROR("6000", "내부 시스템 오류입니다.");

    private final String code;
    private final String Desc;

}
