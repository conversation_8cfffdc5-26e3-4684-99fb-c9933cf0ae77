package com.gowid.corp.core.domain.lotte;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gowid.corp.core.domain.audit.BaseTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Where;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@JsonInclude
@Where(clause = "isDeleted = false")
public class Lotte_D1000 extends BaseTime {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false, updatable = false)
    private Long idx;

    @Column(nullable = false)
    private Long idxCorp;

    @Column
    private String transferDate;

    @Column(columnDefinition = "varchar(13)  DEFAULT '' COMMENT   '사업자등록번호'")
    private String bzno;

    @Column(columnDefinition = "varchar(1)  DEFAULT '' COMMENT   '신규대상여부'")
    private String bzNewYn;
}
