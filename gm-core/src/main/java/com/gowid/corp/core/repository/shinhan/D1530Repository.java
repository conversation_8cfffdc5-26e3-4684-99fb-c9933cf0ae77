package com.gowid.corp.core.repository.shinhan;

import com.gowid.corp.core.domain.shinhan.D1530;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface D1530Repository extends JpaRepository<D1530, Long> {
    Optional<D1530> findFirstByIdxCorpOrderByUpdatedAtDesc(long idxCorp);

    Optional<D1530> findTopByIdxCorp(Long idx);

}
