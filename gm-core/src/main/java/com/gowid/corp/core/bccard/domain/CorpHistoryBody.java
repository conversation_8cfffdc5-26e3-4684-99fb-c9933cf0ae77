package com.gowid.corp.core.bccard.domain;

import com.gowid.corp.core.domain.audit.BaseTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ToString
@Table(name = "corp_history_body", schema = "bc_card")
public class CorpHistoryBody extends  BaseTime{
    @Id
    private String fileName;
    private Long lineNum;

    private String dataDivision;
    private String serialNumber;
    private String registrationNumber;
    private String customerName;
    private String bankCode;
    private String account;
    private String paymentDate;
    private String joinDate;
    private String corporateCompanyNumber;
    private String memberValidDivisionCode;
    private String businessLimit;
    private String companyLimit;
    private String cardNumber;
    private String expirationDate;
    private String cardLimit;
    private String holderDivisionCode;
    private String applicationIssuanceDivisionCode;
    private String holderName;
    private String holderNameEn;
    private String cardRegistrationDate;
    private String cancellationDate;
    private String cardProductNumber;
    private String affiliateMemberId;
    private String cardGradeCode;
    private String isCashCard;
    private String mediaDivision;
    private String cardMemberNumber;
    private String cardProductName;
    private String accidentCode;
    private String accidentRegistrationDivision;
    private String accidentRegistrationDate;
    private String accidentName;
}
