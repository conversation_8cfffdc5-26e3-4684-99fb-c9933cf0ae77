package com.gowid.corp.utils;

import java.util.ArrayList;
import java.util.List;

public class StringUtils {
    public static List<String> splitString(String str, int chunkSize) {
        List<String> result = new ArrayList<>();
        int length = str.length();

        for (int i = 0; i < length; i += chunkSize) {
            result.add(str.substring(i, Math.min(length, i + chunkSize)));
        }
        return result;
    }
    public static String removeHyphen(final String target) {
        return target.replaceAll("-", "");
    }
}
